/**
 * ScriptPersistenceTest - 脚本持久化测试组件
 * 用于测试脚本在页面刷新后是否能正确加载和执行
 */

'use client';

import React, { useState, useEffect } from 'react';

interface ScriptPersistenceTestProps {
  className?: string;
}

export const ScriptPersistenceTest: React.FC<ScriptPersistenceTestProps> = ({ className = '' }) => {
  const [persistentFeatures, setPersistentFeatures] = useState<any[]>([]);
  const [loadedScripts, setLoadedScripts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载persistent-features数据
  const loadPersistentFeatures = async () => {
    try {
      const response = await fetch('/api/persistent-features');
      const result = await response.json();
      if (result.success) {
        setPersistentFeatures(result.data.features || []);
      }
    } catch (error) {
      console.error('加载persistent-features失败:', error);
    }
  };

  // 加载脚本数据
  const loadScripts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/load-scripts');
      const result = await response.json();
      if (result.success) {
        setLoadedScripts(result.data.scripts || []);
      }
    } catch (error) {
      console.error('加载脚本失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadPersistentFeatures();
    loadScripts();
  }, []);

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">脚本持久化测试</h3>

      {/* 操作按钮 */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={loadPersistentFeatures}
          className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
        >
          刷新Persistent Features
        </button>
        <button
          onClick={loadScripts}
          disabled={loading}
          className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? '加载中...' : '刷新脚本列表'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Persistent Features */}
        <div>
          <h4 className="font-medium mb-2">Persistent Features ({persistentFeatures.length})</h4>
          <div className="bg-gray-50 rounded p-3 max-h-60 overflow-y-auto">
            {persistentFeatures.length === 0 ? (
              <p className="text-gray-500 text-sm">无数据</p>
            ) : (
              <div className="space-y-2">
                {persistentFeatures.map((feature, index) => (
                  <div key={feature.id || index} className="bg-white p-2 rounded border text-sm">
                    <div className="font-medium">{feature.name}</div>
                    <div className="text-gray-600 text-xs">{feature.description}</div>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-0.5 rounded text-xs ${
                        feature.isActive 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {feature.isActive ? '活跃' : '非活跃'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(feature.addedAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 加载的脚本 */}
        <div>
          <h4 className="font-medium mb-2">
            加载的脚本 ({loadedScripts.length})
            {loadedScripts.filter(s => s.isActive).length > 0 && (
              <span className="text-green-600 ml-1">
                ({loadedScripts.filter(s => s.isActive).length} 活跃)
              </span>
            )}
          </h4>
          <div className="bg-gray-50 rounded p-3 max-h-60 overflow-y-auto">
            {loadedScripts.length === 0 ? (
              <p className="text-gray-500 text-sm">无数据</p>
            ) : (
              <div className="space-y-2">
                {loadedScripts.map((script, index) => (
                  <div key={script.id || index} className="bg-white p-2 rounded border text-sm">
                    <div className="font-medium">{script.name}</div>
                    <div className="text-gray-600 text-xs">{script.metadata?.description}</div>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-0.5 rounded text-xs ${
                        script.isActive 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {script.isActive ? '活跃' : '非活跃'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(script.createdAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 数据一致性检查 */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <h5 className="font-medium text-yellow-800 mb-2">数据一致性检查</h5>
        <div className="text-sm text-yellow-700">
          <p>• Persistent Features中活跃脚本: {persistentFeatures.filter(f => f.isActive).length}</p>
          <p>• 加载脚本中活跃脚本: {loadedScripts.filter(s => s.isActive).length}</p>
          <p>• 数据是否一致: {
            persistentFeatures.filter(f => f.isActive).length === loadedScripts.filter(s => s.isActive).length
              ? '✅ 一致' 
              : '❌ 不一致'
          }</p>
        </div>
      </div>

      {/* 调试信息 */}
      <details className="mt-4">
        <summary className="cursor-pointer text-sm font-medium text-gray-600">
          调试信息 (点击展开)
        </summary>
        <div className="mt-2 p-3 bg-gray-100 rounded text-xs">
          <div className="mb-2">
            <strong>Persistent Features:</strong>
            <pre className="mt-1 overflow-x-auto">
              {JSON.stringify(persistentFeatures, null, 2)}
            </pre>
          </div>
          <div>
            <strong>Loaded Scripts:</strong>
            <pre className="mt-1 overflow-x-auto">
              {JSON.stringify(loadedScripts, null, 2)}
            </pre>
          </div>
        </div>
      </details>
    </div>
  );
};

export default ScriptPersistenceTest;
