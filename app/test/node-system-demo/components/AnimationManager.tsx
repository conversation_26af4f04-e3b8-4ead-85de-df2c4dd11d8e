/**
 * AnimationManager - 动画管理组件
 * 提供动画列表显示、预览、设置默认等功能
 */

'use client';

import React, { useCallback } from 'react';
import { AnimationInfo } from '../../../../src/types/NodeTypes';

interface AnimationManagerProps {
  nodeId: string;
  animations: AnimationInfo[];
  currentPlayingAnimationId?: string;
  previewingAnimationId?: string;
  onPreviewAnimation: (nodeId: string, animation: AnimationInfo) => void;
  onStopPreview: (nodeId: string) => void;
  onSetDefaultAnimation: (nodeId: string, animation: AnimationInfo) => void;
  onRemoveAnimation: (nodeId: string, animationId: string) => void;
  isUploadingAnimation: boolean;
  animationError: string | null;
}

export const AnimationManager: React.FC<AnimationManagerProps> = ({
  nodeId,
  animations,
  currentPlayingAnimationId,
  previewingAnimationId,
  onPreviewAnimation,
  onStopPreview,
  onSetDefaultAnimation,
  onRemoveAnimation,
  isUploadingAnimation,
  animationError
}) => {
  // 格式化时长显示
  const formatDuration = useCallback((duration: number): string => {
    if (duration < 60) {
      return `${duration.toFixed(1)}秒`;
    }
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}分${seconds.toFixed(1)}秒`;
  }, []);

  // 格式化上传时间
  const formatUploadTime = useCallback((date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString();
  }, []);

  if (animations.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">
        <div className="w-12 h-12 mx-auto mb-3 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
          </svg>
        </div>
        <p className="text-sm">暂无动画文件</p>
        <p className="text-xs mt-1">上传FBX动画文件开始使用</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* 错误提示 */}
      {animationError && (
        <div className="p-3 bg-red-100 border border-red-300 rounded-lg text-sm text-red-800">
          {animationError}
        </div>
      )}

      {/* 动画列表 */}
      <div className="space-y-2">
        {animations.map((animation) => {
          const isPlaying = currentPlayingAnimationId === animation.id;
          const isPreviewing = previewingAnimationId === animation.id;
          const isDefault = animation.isDefault;

          return (
            <div
              key={animation.id}
              className={`p-3 rounded-lg border transition-colors ${
                isDefault
                  ? 'border-blue-500 bg-blue-50'
                  : isPreviewing
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {/* 动画信息头部 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {animation.name}
                    </h4>
                    {isDefault && (
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        默认
                      </span>
                    )}
                    {isPreviewing && (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                        预览中
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>时长: {formatDuration(animation.duration)}</span>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-1 ml-2">
                  {/* 预览按钮 */}
                  {isPreviewing ? (
                    <button
                      onClick={() => onStopPreview(nodeId)}
                      className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-100 rounded transition-colors"
                      title="停止预览"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  ) : (
                    <button
                      onClick={() => onPreviewAnimation(nodeId, animation)}
                      disabled={isUploadingAnimation}
                      className="p-1.5 text-green-600 hover:text-green-700 hover:bg-green-100 rounded transition-colors disabled:opacity-50"
                      title="预览动画"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
                      </svg>
                    </button>
                  )}

                  {/* 设为默认按钮 */}
                  {!isDefault && (
                    <button
                      onClick={() => onSetDefaultAnimation(nodeId, animation)}
                      disabled={isUploadingAnimation}
                      className="p-1.5 text-blue-600 hover:text-blue-700 hover:bg-blue-100 rounded transition-colors disabled:opacity-50"
                      title="设为默认动画"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                  )}

                  {/* 删除按钮 */}
                  <button
                    onClick={() => {
                      if (window.confirm(`确定要删除动画"${animation.name}"吗？`)) {
                        onRemoveAnimation(nodeId, animation.id);
                      }
                    }}
                    disabled={isUploadingAnimation}
                    className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-100 rounded transition-colors disabled:opacity-50"
                    title="删除动画"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* 动画剪辑详情 */}
              {animation.clips.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <div className="grid grid-cols-1 gap-1">
                    {animation.clips.map((clip, index) => (
                      <div key={index} className="flex justify-between text-xs text-gray-600">
                        <span className="truncate">{clip.name}</span>
                        <span>{formatDuration(clip.duration)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AnimationManager;
