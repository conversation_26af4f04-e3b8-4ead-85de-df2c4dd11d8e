/**
 * HTML导出系统类型定义
 * 定义导出配置、项目结构和相关接口
 */

// ==================== 导出配置类型 ====================

/**
 * HTML导出配置
 */
export interface HTMLExportConfig {
  /** 项目基本信息 */
  project: {
    name: string;
    version: string;
    description?: string;
    author?: string;
  };

  /** 输出配置 */
  output: {
    /** 输出文件名 */
    filename: string;
    /** 输出目录 */
    directory: string;
    /** 是否压缩代码 */
    minify: boolean;
    /** 是否内联所有资源 */
    inlineAssets: boolean;
  };

  /** 编译配置 */
  compilation: {
    /** TypeScript编译目标 */
    target: 'ES5' | 'ES6' | 'ES2017' | 'ES2018' | 'ES2019' | 'ES2020';
    /** 模块系统 */
    module: 'CommonJS' | 'AMD' | 'UMD' | 'ES6' | 'IIFE';
    /** 是否启用严格模式 */
    strict: boolean;
    /** 是否生成source map */
    sourceMap: boolean;
  };

  /** 依赖配置 */
  dependencies: {
    /** Babylon.js配置 */
    babylonjs: {
      /** 使用CDN还是本地打包 */
      mode: 'cdn' | 'bundle';
      /** CDN URL（如果使用CDN） */
      cdnUrl?: string;
      /** 版本号 */
      version: string;
      /** 需要的模块 */
      modules: string[];
    };
    /** 其他依赖 */
    external: {
      name: string;
      mode: 'cdn' | 'bundle';
      url?: string;
      version?: string;
    }[];
  };

  /** 资源配置 */
  assets: {
    /** 图片处理 */
    images: {
      /** 是否内联小图片 */
      inlineSmall: boolean;
      /** 内联阈值（字节） */
      inlineThreshold: number;
      /** 压缩质量 */
      quality: number;
    };
    /** 音频处理 */
    audio: {
      /** 是否内联 */
      inline: boolean;
      /** 支持的格式 */
      formats: string[];
    };
    /** 3D模型处理 */
    models: {
      /** 是否内联 */
      inline: boolean;
      /** 压缩级别 */
      compression: 'none' | 'low' | 'medium' | 'high';
    };
  };

  /** 优化配置 */
  optimization: {
    /** 是否移除未使用的代码 */
    treeShaking: boolean;
    /** 是否压缩HTML */
    minifyHTML: boolean;
    /** 是否压缩CSS */
    minifyCSS: boolean;
    /** 是否压缩JavaScript */
    minifyJS: boolean;
    /** 是否优化图片 */
    optimizeImages: boolean;
  };

  /** 调试配置 */
  debug: {
    /** 是否保留调试信息 */
    keepDebugInfo: boolean;
    /** 是否生成详细日志 */
    verbose: boolean;
    /** 是否保留中间文件 */
    keepIntermediateFiles: boolean;
  };
}

// ==================== 项目结构类型 ====================

/**
 * 游戏项目结构
 */
export interface GameProject {
  /** 项目ID */
  id: string;
  /** 项目名称 */
  name: string;
  /** 项目描述 */
  description?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 项目版本 */
  version: string;
  /** 项目配置 */
  config: HTMLExportConfig;
  /** 源文件 */
  sourceFiles: ProjectFile[];
  /** 资源文件 */
  assetFiles: AssetFile[];
  /** 依赖信息 */
  dependencies: ProjectDependency[];
}

/**
 * 项目文件
 */
export interface ProjectFile {
  /** 文件路径 */
  path: string;
  /** 文件名 */
  name: string;
  /** 文件类型 */
  type: 'typescript' | 'javascript' | 'css' | 'html' | 'json';
  /** 文件内容 */
  content: string;
  /** 文件大小（字节） */
  size: number;
  /** 是否为入口文件 */
  isEntry?: boolean;
  /** 修改时间 */
  lastModified: Date;
}

/**
 * 资源文件
 */
export interface AssetFile {
  /** 文件路径 */
  path: string;
  /** 文件名 */
  name: string;
  /** 文件类型 */
  type: 'image' | 'audio' | 'model' | 'texture' | 'other';
  /** 文件URL或base64数据 */
  data: string;
  /** 文件大小（字节） */
  size: number;
  /** MIME类型 */
  mimeType: string;
  /** 是否已内联 */
  inlined: boolean;
}

/**
 * 项目依赖
 */
export interface ProjectDependency {
  /** 依赖名称 */
  name: string;
  /** 版本号 */
  version: string;
  /** 依赖类型 */
  type: 'runtime' | 'development';
  /** 是否为外部依赖 */
  external: boolean;
  /** CDN URL（如果使用CDN） */
  cdnUrl?: string;
  /** 本地路径（如果本地打包） */
  localPath?: string;
}

// ==================== 导出结果类型 ====================

/**
 * 导出结果
 */
export interface ExportResult {
  /** 是否成功 */
  success: boolean;
  /** 输出文件路径 */
  outputPath?: string;
  /** 输出文件大小（字节） */
  outputSize?: number;
  /** 导出耗时（毫秒） */
  duration: number;
  /** 错误信息 */
  error?: string;
  /** 警告信息 */
  warnings: string[];
  /** 详细信息 */
  details: {
    /** 编译统计 */
    compilation: {
      sourceFiles: number;
      compiledFiles: number;
      errors: number;
      warnings: number;
    };
    /** 资源统计 */
    assets: {
      totalFiles: number;
      inlinedFiles: number;
      totalSize: number;
      compressedSize: number;
    };
    /** 依赖统计 */
    dependencies: {
      totalDependencies: number;
      bundledDependencies: number;
      cdnDependencies: number;
    };
  };
}

/**
 * 导出进度信息
 */
export interface ExportProgress {
  /** 当前阶段 */
  stage: 'preparing' | 'compiling' | 'bundling' | 'optimizing' | 'finalizing' | 'complete' | 'error';
  /** 进度百分比 (0-100) */
  progress: number;
  /** 当前任务描述 */
  message: string;
  /** 详细信息 */
  details?: any;
  /** 时间戳 */
  timestamp: Date;
}

// ==================== 模板类型 ====================

/**
 * HTML模板配置
 */
export interface HTMLTemplate {
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** HTML模板内容 */
  html: string;
  /** CSS样式 */
  css?: string;
  /** JavaScript代码 */
  javascript?: string;
  /** 模板变量 */
  variables: {
    [key: string]: string | number | boolean;
  };
  /** 是否为默认模板 */
  isDefault?: boolean;
}

/**
 * 模板变量
 */
export interface TemplateVariables {
  /** 项目标题 */
  title: string;
  /** 项目描述 */
  description: string;
  /** 游戏代码 */
  gameCode: string;
  /** 样式代码 */
  styles: string;
  /** 依赖脚本 */
  dependencies: string[];
  /** 资源数据 */
  assets: { [key: string]: string };
  /** 配置数据 */
  config: any;
}

// ==================== 工具类型 ====================

/**
 * 导出选项
 */
export interface ExportOptions {
  /** 项目ID或项目对象 */
  project: string | GameProject;
  /** 导出配置（可选，使用项目默认配置） */
  config?: Partial<HTMLExportConfig>;
  /** 输出目录（可选，覆盖配置中的目录） */
  outputDir?: string;
  /** 进度回调函数 */
  onProgress?: (progress: ExportProgress) => void;
  /** 是否强制重新导出 */
  force?: boolean;
}

/**
 * 导出器接口
 */
export interface HTMLExporter {
  /** 导出项目 */
  export(options: ExportOptions): Promise<ExportResult>;
  /** 验证项目 */
  validate(project: GameProject): Promise<{ valid: boolean; errors: string[] }>;
  /** 获取默认配置 */
  getDefaultConfig(): HTMLExportConfig;
  /** 预览导出结果 */
  preview(project: GameProject): Promise<string>;
}

// ==================== 默认配置 ====================

/**
 * 默认导出配置
 */
export const DEFAULT_EXPORT_CONFIG: HTMLExportConfig = {
  project: {
    name: 'PlayableGame',
    version: '1.0.0',
    description: 'Generated by PlayableGen',
    author: 'PlayableGen'
  },
  output: {
    filename: 'index.html',
    directory: './dist',
    minify: true,
    inlineAssets: true
  },
  compilation: {
    target: 'ES2017',
    module: 'IIFE',
    strict: true,
    sourceMap: false
  },
  dependencies: {
    threejs: {
      mode: 'cdn',
      cdnUrl: 'https://unpkg.com/three@latest/build/three.min.js',
      version: 'latest',
      modules: ['core', 'loaders', 'controls']
    },
    external: []
  },
  assets: {
    images: {
      inlineSmall: true,
      inlineThreshold: 8192, // 8KB
      quality: 85
    },
    audio: {
      inline: false,
      formats: ['mp3', 'ogg', 'wav']
    },
    models: {
      inline: true,
      compression: 'medium'
    }
  },
  optimization: {
    treeShaking: true,
    minifyHTML: true,
    minifyCSS: true,
    minifyJS: true,
    optimizeImages: true
  },
  debug: {
    keepDebugInfo: false,
    verbose: false,
    keepIntermediateFiles: false
  }
};
