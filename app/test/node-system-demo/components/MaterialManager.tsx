/**
 * MaterialManager - 材质管理组件
 * 提供材质列表显示、预览、设置默认等功能
 */

'use client';

import React, { useCallback } from 'react';
import { MaterialInfo } from '../../../../src/types/NodeTypes';

interface MaterialManagerProps {
  nodeId: string;
  materials: MaterialInfo[];
  currentMaterialId?: string;
  onPreviewMaterial: (nodeId: string, material: MaterialInfo) => void;
  onSetDefaultMaterial: (nodeId: string, material: MaterialInfo) => void;
  onRemoveMaterial: (nodeId: string, materialId: string) => void;
  isUploadingMaterial: boolean;
  materialError: string | null;
}

export const MaterialManager: React.FC<MaterialManagerProps> = ({
  nodeId,
  materials,
  currentMaterialId,
  onPreviewMaterial,
  onSetDefaultMaterial,
  onRemoveMaterial,
  isUploadingMaterial,
  materialError
}) => {
  // 格式化文件大小显示
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  }, []);



  // 获取材质类型显示名称
  const getMaterialTypeName = useCallback((type?: string): string => {
    const typeMap: Record<string, string> = {
      'diffuse': '漫反射',
      'normal': '法线',
      'specular': '镜面反射',
      'roughness': '粗糙度',
      'metallic': '金属度',
      'emissive': '自发光'
    };
    return typeMap[type || ''] || '未知';
  }, []);

  if (materials.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">
        <div className="w-12 h-12 mx-auto mb-3 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-6V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h4a2 2 0 012 2v2m0 0a2 2 0 012 2v2a2 2 0 01-2 2h-2m0 0h-2m0 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2h2z" />
          </svg>
        </div>
        <p className="text-sm">暂无材质文件</p>
        <p className="text-xs mt-1">上传材质贴图文件开始使用</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* 错误提示 */}
      {materialError && (
        <div className="p-3 bg-red-100 border border-red-300 rounded-lg text-sm text-red-800">
          {materialError}
        </div>
      )}

      {/* 材质列表 */}
      <div className="space-y-2">
        {materials.map((material) => {
          const isCurrent = currentMaterialId === material.id;
          const isDefault = material.isDefault;

          return (
            <div
              key={material.id}
              className={`p-3 rounded-lg border transition-colors ${
                isDefault
                  ? 'border-blue-500 bg-blue-50'
                  : isCurrent
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {/* 材质信息头部 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {material.name}
                    </h4>
                    {isDefault && (
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        默认
                      </span>
                    )}
                    {isCurrent && !isDefault && (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                        当前
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>格式: {material.fileType.toUpperCase()}</span>
                    <span>大小: {formatFileSize(material.fileSize)}</span>
                    {material.materialType && (
                      <span>类型: {getMaterialTypeName(material.materialType)}</span>
                    )}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-3">
                  {/* 预览按钮 */}
                  <button
                    onClick={() => onPreviewMaterial(nodeId, material)}
                    className="p-1.5 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                    title="预览材质"
                    disabled={isUploadingMaterial}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>

                  {/* 设为默认按钮 */}
                  {!isDefault && (
                    <button
                      onClick={() => onSetDefaultMaterial(nodeId, material)}
                      className="p-1.5 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                      title="设为默认材质"
                      disabled={isUploadingMaterial}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                  )}

                  {/* 删除按钮 */}
                  <button
                    onClick={() => onRemoveMaterial(nodeId, material.id)}
                    className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                    title="删除材质"
                    disabled={isUploadingMaterial}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>


            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MaterialManager;
