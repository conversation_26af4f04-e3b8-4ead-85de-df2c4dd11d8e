import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

interface PersistentFeature {
  id: string;
  name: string;
  description: string;
  scriptContent: string;
  addedAt: string;
  isActive: boolean;
}

interface ScriptFile {
  id: string;
  name: string;
  content: string;
  createdAt: string;
  lastModified: string;
  isActive: boolean;
  metadata: {
    description: string;
    targetNodeTypes: string[];
    dependencies: string[];
    functionType: string;
  };
}

export async function GET() {
  try {
    const scripts: ScriptFile[] = [];

    // 1. 首先从persistent-features.json加载活跃脚本
    const persistentFeaturesFile = path.join(process.cwd(), 'data', 'persistent-features.json');

    if (fs.existsSync(persistentFeaturesFile)) {
      try {
        const persistentData = fs.readFileSync(persistentFeaturesFile, 'utf-8');
        const persistentFeatures: PersistentFeature[] = JSON.parse(persistentData);

        console.log(`[API] 从persistent-features加载 ${persistentFeatures.length} 个脚本`);

        // 转换persistent features为script格式
        persistentFeatures.forEach(feature => {
          const script = {
            id: feature.id,
            name: feature.name,
            content: feature.scriptContent,
            createdAt: feature.addedAt,
            lastModified: feature.addedAt,
            isActive: feature.isActive, // 保持原有的活跃状态
            metadata: {
              description: feature.description,
              targetNodeTypes: ['mesh'],
              dependencies: ['three'],
              functionType: 'utility'
            }
          };
          scripts.push(script);
        });
      } catch (error) {
        console.error('[API] 读取persistent-features失败:', error);
      }
    }

    // 2. 然后从generated-scripts目录加载其他脚本文件（避免重复）
    const scriptsDir = path.join(process.cwd(), 'generated-scripts');

    if (fs.existsSync(scriptsDir)) {
      const files = fs.readdirSync(scriptsDir);
      const existingIds = new Set(scripts.map(s => s.id));

      // 处理每个脚本文件
      for (const file of files) {
        if (file.endsWith('.js')) {
          const scriptPath = path.join(scriptsDir, file);
          const metadataFile = file.replace('.js', '_metadata.json');
          const metadataPath = path.join(scriptsDir, metadataFile);

          try {
            // 从文件名提取脚本ID
            const fileNameParts = file.replace('.js', '').split('_');
            const scriptId = fileNameParts.length >= 2 ? fileNameParts[1] : Date.now().toString();
            const fullScriptId = `script_${scriptId}`;

            // 跳过已经从persistent-features加载的脚本
            if (existingIds.has(fullScriptId)) {
              continue;
            }

            // 读取脚本内容
            const content = fs.readFileSync(scriptPath, 'utf-8');

            // 读取元数据
            let metadata = {
              description: '场景节点脚本',
              targetNodeTypes: ['mesh'],
              dependencies: ['NodeRegistry', 'NodeCommunicationService'],
              functionType: 'animation' as const,
              userRequirement: '',
              generatedAt: new Date().toISOString(),
              sceneContext: null,
              type: 'scene_script'
            };

            if (fs.existsSync(metadataPath)) {
              const metadataContent = fs.readFileSync(metadataPath, 'utf-8');
              const parsedMetadata = JSON.parse(metadataContent);
              metadata = { ...metadata, ...parsedMetadata };
            }

            const scriptName = fileNameParts.length >= 3 ? fileNameParts.slice(2).join('_') : '未命名脚本';

            // 获取文件时间
            const stats = fs.statSync(scriptPath);

            const script = {
              id: fullScriptId,
              name: scriptName,
              content,
              createdAt: stats.birthtime.toISOString(),
              lastModified: stats.mtime.toISOString(),
              isActive: false, // 从文件系统加载的脚本默认不活跃
              metadata: {
                description: metadata.description,
                targetNodeTypes: metadata.targetNodeTypes,
                dependencies: metadata.dependencies,
                functionType: metadata.functionType
              }
            };

            scripts.push(script);
          } catch (error) {
            console.error(`加载脚本文件失败: ${file}`, error);
            // 继续处理其他文件，不因单个文件错误而失败
          }
        }
      }
    }

    // 按创建时间倒序排列
    scripts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    console.log(`[API] 成功加载 ${scripts.length} 个脚本文件 (${scripts.filter(s => s.isActive).length} 个活跃)`);

    return NextResponse.json({
      success: true,
      data: {
        scripts,
        count: scripts.length,
        activeCount: scripts.filter(s => s.isActive).length
      }
    });

  } catch (error) {
    console.error('[API] 加载脚本文件失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '加载脚本文件失败'
    }, { status: 500 });
  }
}