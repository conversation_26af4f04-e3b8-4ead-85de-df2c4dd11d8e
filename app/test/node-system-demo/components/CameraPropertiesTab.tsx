/**
 * CameraPropertiesTab - 摄像机属性标签页组件
 * 提供摄像机节点专用属性的编辑功能
 */

'use client';

import React, { useState } from 'react';
import { CameraNodeProperties } from '../../../../src/types/NodeTypes';

interface CameraPropertiesTabProps {
  selectedNode: CameraNodeProperties;
  tempInputValues: Record<string, string>;
  onUpdateProperty: (property: string, value: string) => void;
}

export const CameraPropertiesTab: React.FC<CameraPropertiesTabProps> = ({
  selectedNode,
  tempInputValues,
  onUpdateProperty
}) => {
  const [editingNodeName, setEditingNodeName] = useState<string>(selectedNode.name);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  // 当选中的节点改变时，更新编辑状态
  React.useEffect(() => {
    if (!isEditing) {
      setEditingNodeName(selectedNode.name);
    }
  }, [selectedNode.name, isEditing]);

  return (
    <div className="space-y-4">
      {/* 节点名称 */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-900">名称</label>
        <input
          type="text"
          value={editingNodeName}
          onChange={(e) => setEditingNodeName(e.target.value)}
          onFocus={() => setIsEditing(true)}
          onBlur={() => {
            setIsEditing(false);
            if (editingNodeName !== selectedNode.name) {
              onUpdateProperty('name', editingNodeName);
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.currentTarget.blur();
            }
          }}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 位置属性 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">位置 (Position)</label>
        <div className="grid grid-cols-3 gap-2">
          {(['x', 'y', 'z'] as const).map((axis) => (
            <div key={axis}>
              <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
              <input
                type="text"
                value={tempInputValues[`position${axis.toUpperCase()}`] ??
                       selectedNode.position?.[axis]?.toFixed(2) ?? '0'}
                onChange={(e) => onUpdateProperty(`position.${axis}`, e.target.value)}
                className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 旋转属性 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">旋转 (Rotation)</label>
        <div className="grid grid-cols-3 gap-2">
          {(['x', 'y', 'z'] as const).map((axis) => (
            <div key={axis}>
              <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
              <input
                type="text"
                value={tempInputValues[`rotation${axis.toUpperCase()}`] ??
                       selectedNode.rotation?.[axis]?.toFixed(2) ?? '0'}
                onChange={(e) => onUpdateProperty(`rotation.${axis}`, e.target.value)}
                className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 缩放属性 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">缩放 (Scale)</label>
        <div className="grid grid-cols-3 gap-2">
          {(['x', 'y', 'z'] as const).map((axis) => (
            <div key={axis}>
              <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
              <input
                type="text"
                value={tempInputValues[`scaling${axis.toUpperCase()}`] ??
                       selectedNode.scaling?.[axis]?.toFixed(2) ?? '1'}
                onChange={(e) => onUpdateProperty(`scaling.${axis}`, e.target.value)}
                className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 摄像机类型 */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-900">摄像机类型</label>
        <select
          value={selectedNode.cameraType}
          onChange={(e) => onUpdateProperty('cameraType', e.target.value)}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="arc_rotate">弧形旋转摄像机</option>
          <option value="universal">通用摄像机</option>
          <option value="free">自由摄像机</option>
          <option value="follow">跟随摄像机</option>
        </select>
      </div>

      {/* 视野角度 */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-900">视野角度 (FOV)</label>
        <input
          type="number"
          step="0.1"
          min="0.1"
          max="3.14"
          value={tempInputValues['fov'] ?? selectedNode.fov?.toFixed(2) ?? '0.8'}
          onChange={(e) => onUpdateProperty('fov', e.target.value)}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 近裁剪面 */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-900">近裁剪面 (Near)</label>
        <input
          type="number"
          step="0.1"
          min="0.01"
          value={tempInputValues['minZ'] ?? selectedNode.minZ?.toFixed(2) ?? '0.1'}
          onChange={(e) => onUpdateProperty('minZ', e.target.value)}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 远裁剪面 */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-900">远裁剪面 (Far)</label>
        <input
          type="number"
          step="10"
          min="1"
          value={tempInputValues['maxZ'] ?? selectedNode.maxZ?.toFixed(0) ?? '1000'}
          onChange={(e) => onUpdateProperty('maxZ', e.target.value)}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 目标位置 (仅对某些摄像机类型有效) */}
      {(selectedNode.cameraType === 'arc_rotate' || selectedNode.cameraType === 'universal' || selectedNode.cameraType === 'free') && (
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-900">目标位置 (Target)</label>
          <div className="grid grid-cols-3 gap-2">
            {(['x', 'y', 'z'] as const).map((axis) => (
              <div key={axis}>
                <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
                <input
                  type="text"
                  value={tempInputValues[`target${axis.toUpperCase()}`] ??
                         selectedNode.target?.[axis]?.toFixed(2) ?? '0'}
                  onChange={(e) => onUpdateProperty(`target.${axis}`, e.target.value)}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 弧形摄像机特有属性 */}
      {selectedNode.cameraType === 'arc_rotate' && (
        <>
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-900">水平角度 (Alpha)</label>
            <input
              type="number"
              step="0.1"
              value={tempInputValues['alpha'] ?? selectedNode.alpha?.toFixed(2) ?? '0'}
              onChange={(e) => onUpdateProperty('alpha', e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-900">垂直角度 (Beta)</label>
            <input
              type="number"
              step="0.1"
              value={tempInputValues['beta'] ?? selectedNode.beta?.toFixed(2) ?? '0'}
              onChange={(e) => onUpdateProperty('beta', e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-900">半径 (Radius)</label>
            <input
              type="number"
              step="0.5"
              min="0.1"
              value={tempInputValues['radius'] ?? selectedNode.radius?.toFixed(2) ?? '10'}
              onChange={(e) => onUpdateProperty('radius', e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </>
      )}

      {/* 锁定视角 */}
      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={selectedNode.locked ?? false}
            onChange={(e) => onUpdateProperty('locked', e.target.checked.toString())}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm font-medium text-gray-900">锁定视角</span>
        </label>
        <p className="text-xs text-gray-500 mt-1">
          启用后将禁用所有摄像机控制（缩放、移动、旋转等）
        </p>
      </div>

      {/* 摄像机控制设置 */}
      {!selectedNode.locked && (
        <div className="space-y-3 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900">摄像机控制</h4>
          
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedNode.controls?.enabled ?? true}
                onChange={(e) => onUpdateProperty('controls.enabled', e.target.checked.toString())}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">启用控制器</span>
            </label>
          </div>

          {selectedNode.controls?.enabled !== false && (
            <>
              <div>
                <label className="block text-sm text-gray-700 mb-1">平移灵敏度</label>
                <input
                  type="number"
                  step="10"
                  min="1"
                  value={tempInputValues['controls.panningSensibility'] ?? 
                         selectedNode.controls?.panningSensibility?.toString() ?? '50'}
                  onChange={(e) => onUpdateProperty('controls.panningSensibility', e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">滚轮精度</label>
                <input
                  type="number"
                  step="10"
                  min="1"
                  value={tempInputValues['controls.wheelPrecision'] ?? 
                         selectedNode.controls?.wheelPrecision?.toString() ?? '50'}
                  onChange={(e) => onUpdateProperty('controls.wheelPrecision', e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </>
          )}
        </div>
      )}

      {/* 节点信息 */}
      <div className="space-y-3 pt-4 border-t border-gray-200">
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-900">类型</label>
          <p className="text-sm text-gray-700 bg-gray-100 rounded px-3 py-2">
            {selectedNode.type}
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-gray-900">ID</label>
          <p className="text-xs text-gray-600 bg-gray-100 rounded px-3 py-2 font-mono break-all">
            {selectedNode.id}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CameraPropertiesTab;
