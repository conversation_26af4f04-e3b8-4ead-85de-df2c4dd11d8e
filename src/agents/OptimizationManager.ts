/**
 * OptimizationManager - 统一优化管理器
 * 整合所有AI生成优化功能，提供统一的优化接口和管理
 */

import { GameDesign, GameDesignAgent } from './GameDesignAgent';
import { GeneratedCode, CodeGenerationAgent } from './CodeGenerationAgent';
// 移除了模板管理器的依赖，简化优化流程
import { AccuracyOptimizer, ValidationResult } from './AccuracyOptimizer';
import { EfficiencyOptimizer, TokenUsageStats, OptimizationSuggestion } from './EfficiencyOptimizer';
import { TestingValidator, BenchmarkResult } from './TestingValidator';

// 优化配置接口
export interface OptimizationConfig {
  enableCaching: boolean;
  enableTemplateSelection: boolean;
  enableAccuracyValidation: boolean;
  enablePerformanceOptimization: boolean;
  maxIterations: number;
  qualityThreshold: number;
  tokenLimit: number;
}

// 优化结果接口
export interface OptimizationResult {
  success: boolean;
  gameDesign?: GameDesign;
  generatedCode?: GeneratedCode;
  validationResults: {
    design: ValidationResult;
    code?: ValidationResult;
  };
  performance: {
    totalTime: number;
    tokensUsed: number;
    cacheHit: boolean;
    iterations: number;
  };
  quality: {
    overallScore: number;
    meetsStandard: boolean;
  };
  suggestions: OptimizationSuggestion[];
  errors: string[];
}

/**
 * 优化管理器类
 */
export class OptimizationManager {
  private static readonly DEFAULT_CONFIG: OptimizationConfig = {
    enableCaching: true,
    enableTemplateSelection: true,
    enableAccuracyValidation: true,
    enablePerformanceOptimization: true,
    maxIterations: 3,
    qualityThreshold: 75,
    tokenLimit: 20000
  };

  private gameDesignAgent: GameDesignAgent;
  private codeGenerationAgent: CodeGenerationAgent;
  private config: OptimizationConfig;

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = { ...OptimizationManager.DEFAULT_CONFIG, ...config };
    this.gameDesignAgent = new GameDesignAgent();
    this.codeGenerationAgent = new CodeGenerationAgent();
  }

  /**
   * 执行完整的优化生成流程
   */
  public async optimizedGeneration(userInput: string, gameId?: string): Promise<OptimizationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let gameDesign: GameDesign | undefined;
    let generatedCode: GeneratedCode | undefined;
    let tokensUsed = 0;
    let cacheHit = false;
    let iterations = 0;

    try {
      console.log('[OptimizationManager] 开始优化生成流程');

      // 阶段1: 游戏设计生成
      const designResult = await this.optimizedDesignGeneration(userInput);
      gameDesign = designResult.design;
      tokensUsed += designResult.tokensUsed;
      cacheHit = designResult.cacheHit;
      iterations += designResult.iterations;

      if (designResult.errors.length > 0) {
        errors.push(...designResult.errors);
      }

      // 阶段2: 代码生成（如果设计成功）
      if (gameDesign && designResult.validationResult.isValid) {
        const codeResult = await this.optimizedCodeGeneration(gameDesign, gameId);
        generatedCode = codeResult.code;
        tokensUsed += codeResult.tokensUsed;
        iterations += codeResult.iterations;

        if (codeResult.errors.length > 0) {
          errors.push(...codeResult.errors);
        }
      }

      // 验证结果
      const designValidation = gameDesign 
        ? AccuracyOptimizer.validateGameDesign(gameDesign)
        : { isValid: false, score: 0, errors: [], warnings: [], suggestions: [] };

      const codeValidation = generatedCode && gameDesign
        ? AccuracyOptimizer.validateGeneratedCode(generatedCode, gameDesign)
        : undefined;

      // 计算质量分数
      const overallScore = codeValidation
        ? AccuracyOptimizer.calculateOverallQuality(designValidation, codeValidation)
        : designValidation.score;

      const meetsStandard = AccuracyOptimizer.meetsQualityStandard(overallScore);

      // 生成优化建议
      const suggestions = EfficiencyOptimizer.generateOptimizationSuggestions();

      // 记录性能统计
      EfficiencyOptimizer.recordTokenUsage(tokensUsed * 0.3, tokensUsed * 0.7);

      const result: OptimizationResult = {
        success: errors.length === 0 && meetsStandard,
        gameDesign,
        generatedCode,
        validationResults: {
          design: designValidation,
          code: codeValidation
        },
        performance: {
          totalTime: Date.now() - startTime,
          tokensUsed,
          cacheHit,
          iterations
        },
        quality: {
          overallScore,
          meetsStandard
        },
        suggestions,
        errors
      };

      console.log('[OptimizationManager] 优化生成流程完成', {
        success: result.success,
        quality: result.quality,
        performance: result.performance
      });

      return result;

    } catch (error) {
      console.error('[OptimizationManager] 优化生成流程失败:', error);
      
      return {
        success: false,
        validationResults: {
          design: { isValid: false, score: 0, errors: [], warnings: [], suggestions: [] }
        },
        performance: {
          totalTime: Date.now() - startTime,
          tokensUsed,
          cacheHit,
          iterations
        },
        quality: {
          overallScore: 0,
          meetsStandard: false
        },
        suggestions: [],
        errors: [error instanceof Error ? error.message : '未知错误']
      };
    }
  }

  /**
   * 优化的设计生成
   */
  private async optimizedDesignGeneration(userInput: string): Promise<{
    design?: GameDesign;
    validationResult: ValidationResult;
    tokensUsed: number;
    cacheHit: boolean;
    iterations: number;
    errors: string[];
  }> {
    let iterations = 0;
    let tokensUsed = 0;
    let cacheHit = false;
    const errors: string[] = [];

    // 检查缓存
    if (this.config.enableCaching) {
      const cachedDesign = EfficiencyOptimizer.checkDesignCache(userInput);
      if (cachedDesign) {
        cacheHit = true;
        const validationResult = AccuracyOptimizer.validateGameDesign(cachedDesign);
        
        if (validationResult.score >= this.config.qualityThreshold) {
          return {
            design: cachedDesign,
            validationResult,
            tokensUsed: 0,
            cacheHit: true,
            iterations: 0,
            errors: []
          };
        }
      }
    }

    // 迭代生成和优化
    while (iterations < this.config.maxIterations) {
      iterations++;
      
      try {
        // 生成设计
        const design = await this.generateDesignWithOptimization(userInput);
        tokensUsed += 5000; // 估算token使用

        // 验证设计
        const validationResult = AccuracyOptimizer.validateGameDesign(design);

        // 如果质量达标，缓存并返回
        if (validationResult.score >= this.config.qualityThreshold) {
          if (this.config.enableCaching) {
            EfficiencyOptimizer.cacheDesign(userInput, design, 5000);
          }
          
          return {
            design,
            validationResult,
            tokensUsed,
            cacheHit,
            iterations,
            errors
          };
        }

        // 如果质量不达标，记录错误并继续迭代
        errors.push(`第${iterations}次迭代质量不达标，分数: ${validationResult.score}`);

      } catch (error) {
        errors.push(`第${iterations}次迭代失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    // 所有迭代都失败
    return {
      validationResult: { isValid: false, score: 0, errors: [], warnings: [], suggestions: [] },
      tokensUsed,
      cacheHit,
      iterations,
      errors
    };
  }

  /**
   * 优化的代码生成
   */
  private async optimizedCodeGeneration(design: GameDesign, gameId?: string): Promise<{
    code?: GeneratedCode;
    tokensUsed: number;
    iterations: number;
    errors: string[];
  }> {
    let iterations = 0;
    let tokensUsed = 0;
    const errors: string[] = [];

    // 检查缓存
    if (this.config.enableCaching) {
      const designStr = JSON.stringify(design);
      const cachedCode = EfficiencyOptimizer.checkCodeCache(designStr);
      if (cachedCode) {
        const validationResult = AccuracyOptimizer.validateGeneratedCode(cachedCode, design);
        
        if (validationResult.score >= this.config.qualityThreshold) {
          return {
            code: cachedCode,
            tokensUsed: 0,
            iterations: 0,
            errors: []
          };
        }
      }
    }

    // 迭代生成和优化
    while (iterations < this.config.maxIterations) {
      iterations++;
      
      try {
        // 生成代码
        const code = await this.generateCodeWithOptimization(design, gameId);
        tokensUsed += 8000; // 估算token使用

        // 验证代码
        const validationResult = AccuracyOptimizer.validateGeneratedCode(code, design);

        // 如果质量达标，缓存并返回
        if (validationResult.score >= this.config.qualityThreshold) {
          if (this.config.enableCaching) {
            EfficiencyOptimizer.cacheCode(JSON.stringify(design), code, 8000);
          }
          
          return {
            code,
            tokensUsed,
            iterations,
            errors
          };
        }

        // 如果质量不达标，记录错误并继续迭代
        errors.push(`第${iterations}次代码生成质量不达标，分数: ${validationResult.score}`);

      } catch (error) {
        errors.push(`第${iterations}次代码生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    // 所有迭代都失败
    return {
      tokensUsed,
      iterations,
      errors
    };
  }

  /**
   * 带优化的设计生成
   */
  private async generateDesignWithOptimization(userInput: string): Promise<GameDesign> {
    // 模拟调用GameDesignAgent（实际应该调用真实的Agent）
    const result = await this.gameDesignAgent.run(userInput);
    
    if (result.status === 'completed' && result.context.gameDesign) {
      return result.context.gameDesign;
    }
    
    throw new Error('设计生成失败');
  }

  /**
   * 带优化的代码生成
   */
  private async generateCodeWithOptimization(design: GameDesign, gameId?: string): Promise<GeneratedCode> {
    // 调用CodeGenerationAgent，传递gameId以确保文件名一致性
    const threadId = gameId ? `${gameId}_code` : undefined;
    const result = await this.codeGenerationAgent.run(JSON.stringify(design), { threadId });
    
    if (result.status === 'completed' && result.context.generatedCode) {
      return result.context.generatedCode;
    }
    
    throw new Error('代码生成失败');
  }

  /**
   * 运行基准测试
   */
  public async runBenchmark(): Promise<BenchmarkResult> {
    return await TestingValidator.runTestSuite();
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): {
    tokenStats: TokenUsageStats;
    cacheStats: any;
    suggestions: OptimizationSuggestion[];
  } {
    return {
      tokenStats: EfficiencyOptimizer.getTokenStats(),
      cacheStats: EfficiencyOptimizer.getCacheStats(),
      suggestions: EfficiencyOptimizer.generateOptimizationSuggestions()
    };
  }

  /**
   * 重置所有统计数据
   */
  public resetStats(): void {
    EfficiencyOptimizer.resetStats();
    EfficiencyOptimizer.clearAllCaches();
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  public getConfig(): OptimizationConfig {
    return { ...this.config };
  }
}
