import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { userRequirement, sceneContext } = await request.json();

    if (!userRequirement || !userRequirement.trim()) {
      return NextResponse.json({
        success: false,
        error: '用户需求不能为空'
      }, { status: 400 });
    }

    // 流式脚本生成请求

    // 流式响应
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 发送初始状态
          const statusData = JSON.stringify({
            type: 'status',
            data: '📋 开始分析用户需求...'
          });
          controller.enqueue(encoder.encode(`data: ${statusData}\n\n`));

          // 导入真正的流式生成器
          const { CodeGenerationAgent } = await import('../../../src/agents/CodeGenerationAgent');
          const agent = new CodeGenerationAgent();

                     // 模拟分析阶段延迟，然后发送开始生成状态
           await new Promise(resolve => setTimeout(resolve, 800));
           
           const analysisData = JSON.stringify({
             type: 'status',
             data: '🔧 分析完成，开始生成脚本代码...'
           });
           controller.enqueue(encoder.encode(`data: ${analysisData}\n\n`));

          // 直接使用CustomClaude4Client进行流式生成
          let tokenCount = 0;
          let generatedContent = '';

          // 使用agent的generateScriptStream方法
          const streamGenerator = agent.generateScriptStream(userRequirement, sceneContext);

          for await (const chunk of streamGenerator) {
            if (chunk.type === 'token') {
              tokenCount++;
              generatedContent += chunk.data;

              // 实时发送token数据
              const tokenData = JSON.stringify({
                type: 'token',
                data: chunk.data
              });
              controller.enqueue(encoder.encode(`data: ${tokenData}\n\n`));

              // 每50个token发送一次进度更新
              if (tokenCount % 50 === 0) {
                const progressData = JSON.stringify({
                  type: 'progress',
                  data: {
                    current: tokenCount,
                    total: 500, // 估计值
                    message: `已生成 ${tokenCount} 个字符...`
                  }
                });
                controller.enqueue(encoder.encode(`data: ${progressData}\n\n`));
              }
            } else if (chunk.type === 'complete') {
              // 生成完成，使用chunk.data作为最终内容
              generatedContent = chunk.data;
              break;
            }
          }

          // 流式调用完成后处理结果
          const finalContent = generatedContent;

          // 生成完成 - 现在直接生成脚本数据
          const scriptId = `script_${Date.now()}`;
          const now = new Date();
               
               // 从生成的内容中提取脚本名称和描述
               const extractScriptInfo = (content: string, requirement: string) => {
                 // 基于用户需求生成更有意义的脚本名称
                 let name = 'generated_script';
                 let description = requirement.substring(0, 100);

                 // 尝试从用户需求中提取关键词作为脚本名称
                 const keywords = requirement.match(/[\u4e00-\u9fa5a-zA-Z]+/g);
                 if (keywords && keywords.length > 0) {
                   // 取前3个关键词，限制长度
                   const nameKeywords = keywords.slice(0, 3).join('_');
                   if (nameKeywords.length > 0 && nameKeywords.length <= 50) {
                     name = nameKeywords;
                   }
                 }

                 // 也尝试从生成的内容中提取
                 const lines = content.split('\n').slice(0, 20);
                 for (const line of lines) {
                   if (line.includes('脚本名称:') || line.includes('脚本ID:')) {
                     const match = line.match(/脚本名称:\s*([^*\/\n]+)|脚本ID:\s*([^*\/\n]+)/);
                     if (match) {
                       const extractedName = (match[1] || match[2] || '').trim();
                       if (extractedName && extractedName.length <= 50) {
                         name = extractedName;
                       }
                     }
                   }
                   if (line.includes('脚本描述:') || line.includes('功能描述:')) {
                     const match = line.match(/(?:脚本描述|功能描述):\s*([^*\/\n]+)/);
                     if (match) {
                       description = match[1].trim();
                     }
                   }
                 }

                 console.log('[API] 提取的脚本信息:', { name, description });
                 return { name, description };
               };
               
          const { name, description } = extractScriptInfo(finalContent, userRequirement);

          // 清理生成内容，移除markdown代码块标记
          const cleanContent = (content: string): string => {
            // 移除```javascript 开始标记
            let cleaned = content.replace(/^```(?:javascript|js|typescript|ts)?\s*\n?/gm, '');
            // 移除``` 结束标记
            cleaned = cleaned.replace(/\n?```\s*$/gm, '');
            // 移除多余的空行
            cleaned = cleaned.replace(/^\n+/, '').replace(/\n+$/, '');
            return cleaned;
          };

          const cleanedContent = cleanContent(finalContent);

          const completeData = JSON.stringify({
            type: 'complete',
            data: {
              success: true,
              generatedScript: {
                id: scriptId,
                name: name,
                content: cleanedContent, // 使用清理后的内容
                nodeId: null,
                createdAt: now.toISOString(),
                lastModified: now.toISOString(),
                isActive: false,
                metadata: {
                  description: description,
                  targetNodeTypes: ['mesh'],
                  dependencies: ['three'],
                  functionType: 'utility'
                }
              },
              metadata: {
                totalTokens: tokenCount,
                generationTime: Date.now() - Date.now() // 实际计算
              }
            }
          });
          controller.enqueue(encoder.encode(`data: ${completeData}\n\n`));

          controller.close();

        } catch (error) {
          console.error('[API] 流式脚本生成失败:', error);
          
          const errorData = JSON.stringify({
            type: 'error',
            data: {
              message: error instanceof Error ? error.message : '流式生成过程中发生错误'
            }
          });
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });

  } catch (error) {
    console.error('[API] 脚本生成API错误:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 