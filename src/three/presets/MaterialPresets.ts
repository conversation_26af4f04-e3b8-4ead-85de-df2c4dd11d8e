/**
 * Three.js 材质预设类型定义
 * 替代原有的Babylon.js材质预设
 */

// 材质预设类型
export enum MaterialPresetType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  TOON = 'toon',
  METAL = 'metal',
  GLASS = 'glass',
  PLASTIC = 'plastic',
  WOOD = 'wood',
  STONE = 'stone',
  FABRIC = 'fabric',
  NEON = 'neon',
  WATER = 'water'
}

// 材质配置接口
export interface MaterialConfig {
  type: MaterialPresetType;
  materialType: 'basic' | 'standard' | 'physical' | 'phong' | 'lambert' | 'toon';
  color: number;
  metalness?: number;
  roughness?: number;
  opacity?: number;
  transparent?: boolean;
  emissive?: number;
  emissiveIntensity?: number;
  normalScale?: number;
  bumpScale?: number;
  displacementScale?: number;
  envMapIntensity?: number;
  clearcoat?: number;
  clearcoatRoughness?: number;
  transmission?: number;
  thickness?: number;
  ior?: number;
  reflectivity?: number;
  refractionRatio?: number;
  shininess?: number;
  specular?: number;
}

// 预设材质配置
export const MATERIAL_PRESETS: Record<MaterialPresetType, MaterialConfig> = {
  [MaterialPresetType.BASIC]: {
    type: MaterialPresetType.BASIC,
    materialType: 'basic',
    color: 0xffffff
  },

  [MaterialPresetType.STANDARD]: {
    type: MaterialPresetType.STANDARD,
    materialType: 'standard',
    color: 0xffffff,
    metalness: 0.0,
    roughness: 0.5,
    envMapIntensity: 1.0
  },

  [MaterialPresetType.PHYSICAL]: {
    type: MaterialPresetType.PHYSICAL,
    materialType: 'physical',
    color: 0xffffff,
    metalness: 0.0,
    roughness: 0.5,
    clearcoat: 0.0,
    clearcoatRoughness: 0.0,
    transmission: 0.0,
    thickness: 0.5,
    ior: 1.5
  },

  [MaterialPresetType.TOON]: {
    type: MaterialPresetType.TOON,
    materialType: 'toon',
    color: 0xffffff
  },

  [MaterialPresetType.METAL]: {
    type: MaterialPresetType.METAL,
    materialType: 'standard',
    color: 0xc0c0c0,
    metalness: 1.0,
    roughness: 0.1,
    envMapIntensity: 1.0
  },

  [MaterialPresetType.GLASS]: {
    type: MaterialPresetType.GLASS,
    materialType: 'physical',
    color: 0xffffff,
    metalness: 0.0,
    roughness: 0.0,
    transmission: 1.0,
    thickness: 0.5,
    ior: 1.5,
    transparent: true,
    opacity: 0.1
  },

  [MaterialPresetType.PLASTIC]: {
    type: MaterialPresetType.PLASTIC,
    materialType: 'standard',
    color: 0xff4444,
    metalness: 0.0,
    roughness: 0.3,
    envMapIntensity: 0.5
  },

  [MaterialPresetType.WOOD]: {
    type: MaterialPresetType.WOOD,
    materialType: 'standard',
    color: 0x8b4513,
    metalness: 0.0,
    roughness: 0.8,
    normalScale: 0.5,
    bumpScale: 0.3
  },

  [MaterialPresetType.STONE]: {
    type: MaterialPresetType.STONE,
    materialType: 'standard',
    color: 0x808080,
    metalness: 0.0,
    roughness: 0.9,
    normalScale: 1.0,
    bumpScale: 0.5,
    displacementScale: 0.1
  },

  [MaterialPresetType.FABRIC]: {
    type: MaterialPresetType.FABRIC,
    materialType: 'standard',
    color: 0x4169e1,
    metalness: 0.0,
    roughness: 0.7,
    normalScale: 0.3
  },

  [MaterialPresetType.NEON]: {
    type: MaterialPresetType.NEON,
    materialType: 'standard',
    color: 0x00ffff,
    metalness: 0.0,
    roughness: 0.1,
    emissive: 0x00ffff,
    emissiveIntensity: 0.5
  },

  [MaterialPresetType.WATER]: {
    type: MaterialPresetType.WATER,
    materialType: 'physical',
    color: 0x006994,
    metalness: 0.0,
    roughness: 0.1,
    transmission: 0.8,
    thickness: 0.5,
    ior: 1.33,
    transparent: true,
    opacity: 0.8,
    envMapIntensity: 1.0
  }
};

/**
 * 获取材质预设配置
 */
export function getMaterialPreset(type: MaterialPresetType): MaterialConfig {
  return MATERIAL_PRESETS[type];
}

/**
 * 根据对象类型推荐材质预设
 */
export function recommendMaterialForObject(objectType: string): MaterialPresetType {
  switch (objectType.toLowerCase()) {
    case 'character':
    case 'player':
      return MaterialPresetType.STANDARD;
    case 'building':
    case 'wall':
      return MaterialPresetType.STONE;
    case 'ground':
    case 'floor':
      return MaterialPresetType.STONE;
    case 'tree':
    case 'plant':
      return MaterialPresetType.STANDARD;
    case 'water':
    case 'ocean':
    case 'lake':
      return MaterialPresetType.WATER;
    case 'metal':
    case 'weapon':
    case 'tool':
      return MaterialPresetType.METAL;
    case 'glass':
    case 'window':
      return MaterialPresetType.GLASS;
    case 'ui':
    case 'interface':
      return MaterialPresetType.NEON;
    default:
      return MaterialPresetType.STANDARD;
  }
}

/**
 * 根据游戏风格推荐材质预设
 */
export function recommendMaterialForStyle(style: string): MaterialPresetType {
  switch (style.toLowerCase()) {
    case 'cartoon':
    case 'toon':
    case 'anime':
      return MaterialPresetType.TOON;
    case 'realistic':
    case 'photorealistic':
      return MaterialPresetType.PHYSICAL;
    case 'cyberpunk':
    case 'neon':
    case 'futuristic':
      return MaterialPresetType.NEON;
    case 'minimalist':
    case 'simple':
      return MaterialPresetType.BASIC;
    default:
      return MaterialPresetType.STANDARD;
  }
}
