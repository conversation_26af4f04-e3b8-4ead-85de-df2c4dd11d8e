/**
 * NodePropertiesPanel - 节点属性面板组件（重构版）
 * 提供节点属性的查看和编辑功能，使用拆分的子组件
 */

'use client';

import React, { useCallback, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { GameNodeType, AnimationInfo, MeshNodeProperties, CameraNodeProperties, AnimationClipInfo, ModelInfo, MaterialInfo } from '../../../../src/types/NodeTypes';
import { fbxModelLoader } from '../../../../src/three/loaders/FBXModelLoader';
import TabPanel from './TabPanel';
import BasicPropertiesTab from './BasicPropertiesTab';
import CameraPropertiesTab from './CameraPropertiesTab';
import ModelAnimationTab from './ModelAnimationTab';
import MaterialTab from './MaterialTab';

interface NodePropertiesPanelProps {
  className?: string;
}

export const NodePropertiesPanel: React.FC<NodePropertiesPanelProps> = ({ className = '' }) => {
  const {
    nodes,
    selectedNode,
    setSelectedNode,
    setNodes,
    nodeStats,
    tempInputValues,
    setTempInputValues,
    isUploadingModel,
    uploadError,
    isDragOver,
    setIsUploadingModel,
    setUploadError,
    setIsDragOver,
    threeContext,
    addAnimation,
    removeAnimation,
    setCurrentPlayingAnimation,
    setPreviewingAnimation,
    setIsUploadingAnimation,
    setAnimationError,
    isUploadingAnimation,
    animationError,
    addModel,
    removeModel,
    setCurrentModel,
    getNodeModels,
    addMaterial,
    removeMaterial,
    setCurrentMaterial,
    getNodeMaterials,
    getNodeAnimations,
    currentMaterial
  } = useNodeSystem();

  const [isDragOverAnimation, setIsDragOverAnimation] = useState<boolean>(false);
  const [isDragOverMaterial, setIsDragOverMaterial] = useState<boolean>(false);
  const [isUploadingMaterial, setIsUploadingMaterial] = useState<boolean>(false);
  const [materialError, setMaterialError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);
  const [, setAnimationMixer] = useState<THREE.AnimationMixer | null>(null);

  // 加载动画元数据
  const loadAnimationMetadata = useCallback(async (animationPath: string): Promise<{ duration: number; clips: AnimationClipInfo[] }> => {
    try {
      const fbxLoader = new FBXLoader();
      const animationFBX = await new Promise<THREE.Group>((resolve, reject) => {
        fbxLoader.load(
          animationPath,
          (object: THREE.Group) => resolve(object),
          undefined,
          (error: unknown) => reject(error)
        );
      });

      if (animationFBX.animations.length === 0) {
        return { duration: 0, clips: [] };
      }

      const clips: AnimationClipInfo[] = animationFBX.animations.map(clip => ({
        name: clip.name || 'Unnamed',
        duration: clip.duration,
        tracks: clip.tracks.length
      }));

      const totalDuration = Math.max(...clips.map(clip => clip.duration));

      return { duration: totalDuration, clips };
    } catch (error) {
      console.error('[动画元数据] 加载失败:', error);
      return { duration: 0, clips: [] };
    }
  }, []);

  // 应用模型到节点
  const applyModelToNode = useCallback(async (nodeId: string, modelPath: string, fileExtension: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[模型应用] 未找到目标节点:', nodeId);
      return;
    }

    try {
      let newModel: THREE.Object3D;

      if (fileExtension === '.fbx') {
        console.log('[模型应用] 使用FBXLoader加载模型:', modelPath);
        const fbxModel = await fbxModelLoader.loadModel({
          url: modelPath,
          enableShadows: true,
          scale: 1
        });
        newModel = fbxModel.object;
      } else if (fileExtension === '.glb' || fileExtension === '.gltf') {
        console.log('[模型应用] 使用GLTFLoader加载模型:', modelPath);
        const gltfLoader = new GLTFLoader();
        const gltf = await new Promise<any>((resolve, reject) => {
          gltfLoader.load(
            modelPath,
            (gltf) => resolve(gltf),
            undefined,
            (error) => reject(error)
          );
        });
        newModel = gltf.scene;
      } else {
        throw new Error('不支持的模型格式');
      }

      // 复制原有对象的变换属性
      newModel.position.copy(existingObject.position);
      newModel.rotation.copy(existingObject.rotation);
      newModel.scale.copy(existingObject.scale);
      newModel.name = existingObject.name;

      // 移除旧对象，添加新对象
      scene.remove(existingObject);
      scene.add(newModel);

      console.log('[模型应用] 模型已成功应用到节点:', nodeId);

    } catch (error) {
      console.error('[模型应用] 模型应用失败:', error);
      throw new Error('模型加载失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [threeContext]);

  // 更新节点属性
  const updateNodeProperty = useCallback((property: string, value: string) => {
    if (!selectedNode || !threeContext) return;

    const { scene } = threeContext;
    const sceneObject = scene.getObjectByName(selectedNode.id);

    // 处理嵌套属性（如 position.x）
    const keys = property.split('.');
    const updatedNode = { ...selectedNode };

    if (keys.length === 2) {
      const [parentKey, childKey] = keys;
      if (parentKey === 'position' || parentKey === 'rotation' || parentKey === 'scale') {
        const numValue = parseFloat(value) || 0;
        const finalValue = parentKey === 'rotation' ? (numValue * Math.PI) / 180 : numValue;

        // 处理scaling属性的映射
        const nodePropertyKey = parentKey === 'scale' ? 'scaling' : parentKey;

        if (!updatedNode[nodePropertyKey as keyof typeof updatedNode]) {
          (updatedNode as Record<string, any>)[nodePropertyKey] = new THREE.Vector3();
        }
        ((updatedNode as Record<string, any>)[nodePropertyKey] as THREE.Vector3)[childKey as 'x' | 'y' | 'z'] = finalValue;

        // 同步更新Three.js场景中的对象
        if (sceneObject) {
          if (parentKey === 'position') {
            sceneObject.position[childKey as 'x' | 'y' | 'z'] = finalValue;
          } else if (parentKey === 'rotation') {
            sceneObject.rotation[childKey as 'x' | 'y' | 'z'] = finalValue;
          } else if (parentKey === 'scale') {
            sceneObject.scale[childKey as 'x' | 'y' | 'z'] = finalValue;
          }
        }

        // 更新临时输入值
        setTempInputValues({
          [`${parentKey}${childKey.toUpperCase()}`]: value
        });
      }
    } else {
      // 处理摄像机特有属性
      if (selectedNode.type === GameNodeType.CAMERA && sceneObject) {
        const camera = sceneObject as THREE.Camera;

        if (property === 'fov') {
          const fovValue = parseFloat(value) || 0.8;
          (updatedNode as CameraNodeProperties).fov = fovValue;
          if ('fov' in camera) {
            (camera as THREE.PerspectiveCamera).fov = fovValue * (180 / Math.PI); // 转换为度数
            (camera as THREE.PerspectiveCamera).updateProjectionMatrix();
          }
        } else if (property === 'minZ') {
          const minZValue = parseFloat(value) || 0.1;
          (updatedNode as CameraNodeProperties).minZ = minZValue;
          camera.near = minZValue;
          if ('updateProjectionMatrix' in camera) {
            (camera as THREE.PerspectiveCamera).updateProjectionMatrix();
          }
        } else if (property === 'maxZ') {
          const maxZValue = parseFloat(value) || 1000;
          (updatedNode as CameraNodeProperties).maxZ = maxZValue;
          camera.far = maxZValue;
          if ('updateProjectionMatrix' in camera) {
            (camera as THREE.PerspectiveCamera).updateProjectionMatrix();
          }
        } else if (property === 'locked') {
          const lockedValue = value === 'true';
          (updatedNode as CameraNodeProperties).locked = lockedValue;
          // 这里可以添加控制器锁定逻辑
          console.log('[摄像机] 锁定状态更新:', lockedValue);
        } else if (property.startsWith('target.')) {
          const axis = property.split('.')[1] as 'x' | 'y' | 'z';
          const targetValue = parseFloat(value) || 0;
          if (!updatedNode.target) {
            (updatedNode as CameraNodeProperties).target = new THREE.Vector3();
          }
          (updatedNode as CameraNodeProperties).target![axis] = targetValue;

          // 更新Three.js摄像机目标
          if ('setTarget' in camera) {
            (camera as any).setTarget((updatedNode as CameraNodeProperties).target);
          }
        } else if (property.startsWith('controls.')) {
          const controlProperty = property.split('.')[1];
          if (!updatedNode.controls) {
            (updatedNode as CameraNodeProperties).controls = { enabled: true };
          }

          if (controlProperty === 'enabled') {
            (updatedNode as CameraNodeProperties).controls!.enabled = value === 'true';
          } else if (controlProperty === 'panningSensibility') {
            (updatedNode as CameraNodeProperties).controls!.panningSensibility = parseFloat(value) || 50;
          } else if (controlProperty === 'wheelPrecision') {
            (updatedNode as CameraNodeProperties).controls!.wheelPrecision = parseFloat(value) || 50;
          }
        } else {
          (updatedNode as Record<string, any>)[property] = value;
        }
      } else {
        (updatedNode as Record<string, any>)[property] = value;

        // 同步更新Three.js场景中的对象的其他属性
        // 注意：不要更新sceneObject.name，因为它用于通过ID查找对象
        if (sceneObject && property === 'enabled') {
          // enabled属性可能需要特殊处理
          sceneObject.userData.enabled = value === 'true';
        }

        // 对于name属性，我们只更新React状态，不更新Three.js对象的name
        // 因为Three.js对象的name用于通过ID查找，应该保持为节点ID
      }
    }

    // 更新节点列表
    const updatedNodes = nodes.map(node =>
      node.id === selectedNode.id ? updatedNode : node
    );
    setNodes(updatedNodes);

    // 同时更新selectedNode状态，确保UI显示最新的值
    setSelectedNode(updatedNode);

    console.log('[节点属性] 属性已更新:', property, value, '场景对象:', sceneObject);
  }, [selectedNode, nodes, setNodes, setSelectedNode, setTempInputValues, threeContext]);

  // 处理模型文件上传
  const handleModelUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingModel(true);
    setUploadError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.fbx', '.glb', '.gltf'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('模型文件仅支持 .fbx、.glb 和 .gltf 格式');
      }

      console.log('[模型上传] 开始上传模型文件:', file.name, 'to node:', nodeId);

      // 生成模型ID
      const modelId = `model_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('modelId', modelId);
      formData.append('type', 'model');

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '模型文件上传失败');
      }

      console.log('[模型上传] 文件上传成功:', uploadResult.data.filePath);

      // 2. 创建模型信息对象
      const modelInfo: ModelInfo = {
        id: modelId,
        name: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
        filePath: uploadResult.data.filePath,
        fileSize: file.size,
        fileType: fileExtension.substring(1) as 'fbx' | 'glb' | 'gltf',
        isDefault: false, // 新上传的模型不设为默认
        uploadedAt: new Date(),
        meshCount: 0, // 将在加载后更新
        materialCount: 0, // 将在加载后更新
        hasAnimations: false // 将在加载后更新
      };

      // 3. 添加到模型管理系统
      addModel(nodeId, modelInfo);

      // 4. 加载并应用模型到Three.js场景
      if (threeContext) {
        await applyModelToNode(nodeId, uploadResult.data.filePath, fileExtension);
      }

      console.log('[模型上传] 模型应用成功');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      setUploadError(errorMessage);
      console.error('[模型上传] 上传失败:', error);
    } finally {
      setIsUploadingModel(false);
    }
  }, [setIsUploadingModel, setUploadError, threeContext, applyModelToNode, addModel]);

  // 处理动画文件上传
  const handleAnimationUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingAnimation(true);
    setAnimationError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.fbx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('动画文件仅支持 .fbx 格式');
      }

      console.log('[动画上传] 开始上传动画文件:', file.name, 'to node:', nodeId);

      // 生成动画ID
      const animationId = `anim_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('animationId', animationId);
      formData.append('type', 'animation');

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '动画文件上传失败');
      }

      console.log('[动画上传] 动画文件上传成功:', uploadResult.data.filePath);

      // 2. 加载动画文件获取元数据
      const animationMetadata = await loadAnimationMetadata(uploadResult.data.filePath);

      // 3. 创建动画信息对象
      const animationInfo: AnimationInfo = {
        id: animationId,
        name: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
        filePath: uploadResult.data.filePath,
        duration: animationMetadata.duration,
        isDefault: false, // 新上传的动画不设为默认
        uploadedAt: new Date(),
        clips: animationMetadata.clips
      };

      // 4. 添加到动画管理系统
      addAnimation(nodeId, animationInfo);

      // 5. 更新节点属性
      const updatedNodes = nodes.map(node => {
        if (node.id === nodeId && node.type === GameNodeType.MESH) {
          const meshNode = node as MeshNodeProperties;
          const existingAnimations = meshNode.animations || [];
          return {
            ...meshNode,
            animations: [...existingAnimations, animationInfo]
          };
        }
        return node;
      });
      setNodes(updatedNodes);

      console.log('[动画上传] 动画信息已保存到节点属性');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '动画上传失败';
      setAnimationError(errorMessage);
      console.error('[动画上传] 动画上传失败:', error);
    } finally {
      setIsUploadingAnimation(false);
    }
  }, [setIsUploadingAnimation, setAnimationError, addAnimation, nodes, setNodes, loadAnimationMetadata]);

  // 预览动画
  const previewAnimation = useCallback(async (nodeId: string, animationInfo: AnimationInfo) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[动画预览] 未找到目标节点:', nodeId);
      return;
    }

    try {
      console.log('[动画预览] 开始预览动画:', animationInfo.name);

      // 设置预览状态
      setPreviewingAnimation(nodeId, animationInfo.id);

      // 使用FBXLoader加载动画文件
      const fbxLoader = new FBXLoader();
      const animationFBX = await new Promise<THREE.Group>((resolve, reject) => {
        fbxLoader.load(
          animationInfo.filePath,
          (object: THREE.Group) => resolve(object),
          undefined,
          (error: unknown) => reject(error)
        );
      });

      if (animationFBX.animations.length === 0) {
        throw new Error('动画文件中未找到动画数据');
      }

      // 获取或创建AnimationMixer
      let mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
      if (!mixer) {
        mixer = new THREE.AnimationMixer(existingObject);
        // 设置合适的时间缩放，与默认动画保持一致
        mixer.timeScale = 0.5; // 降低到一半速度，与默认动画一致
        existingObject.userData.animationMixer = mixer;
        setAnimationMixer(mixer);
      }

      // 停止所有现有动画
      mixer.stopAllAction();

      // 播放预览动画
      animationFBX.animations.forEach((clip) => {
        const action = mixer.clipAction(clip);
        action.setLoop(THREE.LoopRepeat, Infinity);
        action.play();
      });

      console.log('[动画预览] 预览动画播放成功');

    } catch (error) {
      console.error('[动画预览] 预览失败:', error);
      setAnimationError('动画预览失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [threeContext, setPreviewingAnimation, setAnimationMixer, setAnimationError]);

  // 停止预览动画
  const stopPreviewAnimation = useCallback((nodeId: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) return;

    const mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
    if (mixer) {
      mixer.stopAllAction();
    }

    setPreviewingAnimation(nodeId, null);
    console.log('[动画预览] 已停止预览');
  }, [threeContext, setPreviewingAnimation]);

  // 设置默认动画
  const setDefaultAnimation = useCallback(async (nodeId: string, animationInfo: AnimationInfo) => {
    try {
      // 更新Context中的动画数据，将指定动画设为默认
      const nodeAnimations = getNodeAnimations(nodeId);

      // 清除所有动画的默认状态，然后设置新的默认动画
      nodeAnimations.forEach(animation => {
        if (animation.id !== animationInfo.id) {
          addAnimation(nodeId, { ...animation, isDefault: false });
        }
      });

      // 设置新的默认动画
      addAnimation(nodeId, { ...animationInfo, isDefault: true });

      // 设置当前播放动画
      setCurrentPlayingAnimation(nodeId, animationInfo.id);

      // 应用动画到场景
      await previewAnimation(nodeId, animationInfo);

      console.log('[动画设置] 已设置默认动画:', animationInfo.name);
    } catch (error) {
      console.error('[动画设置] 设置默认动画失败:', error);
      setAnimationError('设置默认动画失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [getNodeAnimations, addAnimation, setCurrentPlayingAnimation, previewAnimation, setAnimationError]);

  // 模型管理函数
  const previewModel = useCallback(async (nodeId: string, modelInfo: ModelInfo) => {
    console.log('[模型预览] 预览模型:', modelInfo.name);
    setCurrentModel(nodeId, modelInfo.id);
    // TODO: 实现模型预览逻辑
  }, [setCurrentModel]);

  const setDefaultModel = useCallback(async (nodeId: string, modelInfo: ModelInfo) => {
    console.log('[模型设置] 设置默认模型:', modelInfo.name);
    // TODO: 实现设置默认模型逻辑
  }, []);

  const removeModelHandler = useCallback(async (nodeId: string, modelId: string) => {
    console.log('[模型删除] 删除模型:', modelId);
    removeModel(nodeId, modelId);
  }, [removeModel]);

  // 材质管理函数
  const handleMaterialUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingMaterial(true);
    setMaterialError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tga', '.exr', '.hdr'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('材质文件支持格式: .jpg, .jpeg, .png, .bmp, .tga, .exr, .hdr');
      }

      console.log('[材质上传] 开始上传材质文件:', file.name, 'to node:', nodeId);

      // 生成材质ID
      const materialId = `mat_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('materialId', materialId);
      formData.append('type', 'material');

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '材质文件上传失败');
      }

      console.log('[材质上传] 材质文件上传成功:', uploadResult.data.filePath);

      // 2. 创建材质信息对象
      const materialInfo: MaterialInfo = {
        id: materialId,
        name: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
        filePath: uploadResult.data.filePath,
        fileSize: file.size,
        fileType: fileExtension.substring(1),
        isDefault: false,
        uploadedAt: new Date(),
        materialType: 'diffuse' // 默认为漫反射材质
      };

      // 3. 添加到材质管理系统
      addMaterial(nodeId, materialInfo);

      console.log('[材质上传] 材质信息已保存');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '材质上传失败';
      setMaterialError(errorMessage);
      console.error('[材质上传] 材质上传失败:', error);
    } finally {
      setIsUploadingMaterial(false);
    }
  }, [setIsUploadingMaterial, setMaterialError, addMaterial]);

  const previewMaterial = useCallback(async (nodeId: string, materialInfo: MaterialInfo) => {
    if (!threeContext) {
      console.warn('[材质预览] Three.js上下文未初始化');
      return;
    }

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[材质预览] 未找到目标节点:', nodeId);
      return;
    }

    try {
      console.log('[材质预览] 开始预览材质:', materialInfo.name);

      // 加载材质纹理
      const textureLoader = new THREE.TextureLoader();
      const texture = await new Promise<THREE.Texture>((resolve, reject) => {
        textureLoader.load(
          materialInfo.filePath,
          (loadedTexture) => {
            // 设置纹理参数
            loadedTexture.wrapS = THREE.RepeatWrapping;
            loadedTexture.wrapT = THREE.RepeatWrapping;
            loadedTexture.flipY = false;
            resolve(loadedTexture);
          },
          undefined,
          (error) => reject(error)
        );
      });

      // 应用材质到模型
      existingObject.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // 根据材质类型创建不同的材质
          let newMaterial: THREE.Material;

          switch (materialInfo.materialType) {
            case 'normal':
              // 法线贴图
              newMaterial = new THREE.MeshStandardMaterial({
                normalMap: texture,
                color: 0xffffff
              });
              break;
            case 'roughness':
              // 粗糙度贴图
              newMaterial = new THREE.MeshStandardMaterial({
                roughnessMap: texture,
                color: 0xffffff,
                metalness: 0.0
              });
              break;
            case 'metallic':
              // 金属度贴图
              newMaterial = new THREE.MeshStandardMaterial({
                metalnessMap: texture,
                color: 0xffffff,
                roughness: 0.5
              });
              break;
            case 'emissive':
              // 自发光贴图
              newMaterial = new THREE.MeshStandardMaterial({
                emissiveMap: texture,
                emissive: 0xffffff,
                emissiveIntensity: 0.5
              });
              break;
            case 'diffuse':
            default:
              // 漫反射贴图（默认）
              newMaterial = new THREE.MeshStandardMaterial({
                map: texture,
                color: 0xffffff
              });
              break;
          }

          // 保存原始材质（用于恢复）
          if (!child.userData.originalMaterial) {
            child.userData.originalMaterial = child.material;
          }

          // 应用新材质
          child.material = newMaterial;
          child.material.needsUpdate = true;
        }
      });

      // 设置当前材质状态
      setCurrentMaterial(nodeId, materialInfo.id);

      console.log('[材质预览] 材质预览成功:', materialInfo.name);

    } catch (error) {
      console.error('[材质预览] 材质预览失败:', error);
      setMaterialError('材质预览失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [threeContext, setCurrentMaterial, setMaterialError]);

  const setDefaultMaterial = useCallback(async (nodeId: string, materialInfo: MaterialInfo) => {
    console.log('[材质设置] 设置默认材质:', materialInfo.name);

    try {
      // 先预览材质
      await previewMaterial(nodeId, materialInfo);

      // 更新Context中的材质数据，将指定材质设为默认
      const nodeMaterials = getNodeMaterials(nodeId);

      // 清除所有材质的默认状态，然后设置新的默认材质
      nodeMaterials.forEach(material => {
        if (material.id !== materialInfo.id) {
          addMaterial(nodeId, { ...material, isDefault: false });
        }
      });

      // 设置新的默认材质
      addMaterial(nodeId, { ...materialInfo, isDefault: true });

      console.log('[材质设置] 已设置默认材质:', materialInfo.name);
    } catch (error) {
      console.error('[材质设置] 设置默认材质失败:', error);
      setMaterialError('设置默认材质失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [previewMaterial, getNodeMaterials, addMaterial, setMaterialError]);

  const removeMaterialHandler = useCallback(async (nodeId: string, materialId: string) => {
    console.log('[材质删除] 删除材质:', materialId);

    // 如果删除的是当前应用的材质，恢复原始材质
    const currentMaterialId = currentMaterial.get(nodeId);
    if (currentMaterialId === materialId && threeContext) {
      const { scene } = threeContext;
      const existingObject = scene.getObjectByName(nodeId);

      if (existingObject) {
        existingObject.traverse((child) => {
          if (child instanceof THREE.Mesh && child.userData.originalMaterial) {
            child.material = child.userData.originalMaterial;
            child.material.needsUpdate = true;
            delete child.userData.originalMaterial;
          }
        });

        // 清除当前材质状态
        setCurrentMaterial(nodeId, null);
      }
    }

    removeMaterial(nodeId, materialId);
  }, [removeMaterial, threeContext, setCurrentMaterial, currentMaterial]);

  // 保存节点配置
  const saveNodeConfiguration = useCallback(async () => {
    if (!selectedNode) {
      console.warn('[节点保存] 没有选中的节点');
      return;
    }

    setIsSaving(true);
    setSaveMessage(null);

    try {
      console.log('[节点保存] 开始保存节点配置:', selectedNode.id);

      // 等待一小段时间，确保Context更新完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 准备保存的节点数据，包含模型和材质信息
      const nodeToSave = {
        id: selectedNode.id,
        name: selectedNode.name,
        type: selectedNode.type,
        position: {
          x: selectedNode.position.x,
          y: selectedNode.position.y,
          z: selectedNode.position.z
        },
        rotation: {
          x: selectedNode.rotation.x,
          y: selectedNode.rotation.y,
          z: selectedNode.rotation.z
        },
        scaling: {
          x: selectedNode.scaling.x,
          y: selectedNode.scaling.y,
          z: selectedNode.scaling.z
        },
        // 从Context获取模型信息
        models: selectedNode.type === GameNodeType.MESH ?
          getNodeModels(selectedNode.id) : undefined,
        // 从Context获取材质信息
        materials: selectedNode.type === GameNodeType.MESH ?
          getNodeMaterials(selectedNode.id) : undefined,
        // 从Context获取动画信息
        animations: selectedNode.type === GameNodeType.MESH ?
          getNodeAnimations(selectedNode.id) : undefined,
        // 保留颜色信息（如果存在）
        color: selectedNode.type === GameNodeType.MESH &&
               (selectedNode as MeshNodeProperties).material?.diffuseColor ? {
          r: (selectedNode as MeshNodeProperties).material!.diffuseColor!.r,
          g: (selectedNode as MeshNodeProperties).material!.diffuseColor!.g,
          b: (selectedNode as MeshNodeProperties).material!.diffuseColor!.b
        } : undefined
      };

      // 添加调试日志，查看保存的数据
      console.log('[节点保存] 准备保存的数据:', nodeToSave);
      if (nodeToSave.materials) {
        console.log('[节点保存] 材质数据:', nodeToSave.materials.map(m => ({ name: m.name, isDefault: m.isDefault })));
      }
      if (nodeToSave.animations) {
        console.log('[节点保存] 动画数据:', nodeToSave.animations.map(a => ({ name: a.name, isDefault: a.isDefault })));
      }

      // 调用保存API
      const response = await fetch('/api/node-properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodeProperty: nodeToSave
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('[节点保存] 节点配置保存成功:', result.message);
        setSaveMessage('保存成功！');
        // 3秒后清除消息
        setTimeout(() => setSaveMessage(null), 3000);
      } else {
        console.error('[节点保存] 节点配置保存失败:', result.error);
        setSaveMessage('保存失败: ' + result.error);
      }

    } catch (error) {
      console.error('[节点保存] 保存过程中发生错误:', error);
      setSaveMessage('保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsSaving(false);
    }
  }, [selectedNode, getNodeModels, getNodeMaterials, getNodeAnimations]);

  // 如果没有选中节点，显示节点列表
  if (!selectedNode) {
    return (
      <div className={`w-96 bg-white border-l border-gray-200 flex flex-col ${className}`}>
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-bold text-gray-900">🎛️ 节点属性</h3>
          <p className="text-sm text-gray-600">选择一个节点来编辑属性</p>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            {nodes.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">暂无节点</p>
                <p className="text-xs mt-1">点击『添加方块』创建节点</p>
              </div>
            ) : (
              <div className="space-y-2">
                {nodes.map((node) => (
                  <div
                    key={node.id}
                    onClick={() => setSelectedNode(node)}
                    className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {node.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {node.type}
                        </p>
                      </div>
                    </div>

                    {/* 位置信息 */}
                    <div className="mt-2 text-xs text-gray-600">
                      {node.position ? (
                        `位置: (${node.position.x?.toFixed(1) ?? '0'}, ${node.position.y?.toFixed(1) ?? '0'}, ${node.position.z?.toFixed(1) ?? '0'})`
                      ) : (
                        '位置: 未设置'
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 节点统计 */}
          {Object.keys(nodeStats.byType).length > 0 && (
            <div className="px-4 pb-4">
              <h5 className="text-xs font-medium text-gray-700 mb-2">类型统计</h5>
              <div className="space-y-1">
                {Object.entries(nodeStats.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between text-xs">
                    <span className="text-gray-600">{type}</span>
                    <span className="text-gray-900 font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`w-96 bg-white border-l border-gray-200 flex flex-col ${className}`}>
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-bold text-gray-900">🎛️ 节点属性</h3>
            <p className="text-sm text-gray-600">编辑选中节点的属性</p>
          </div>
          <button
            onClick={() => setSelectedNode(null)}
            className="text-gray-500 hover:text-gray-700 p-1 rounded transition-colors"
            title="返回节点列表"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <TabPanel
          tabs={[
            // 基础属性标签页 - 对非摄像机节点显示
            {
              id: 'basic',
              label: '基础属性',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              ),
              content: (
                <BasicPropertiesTab
                  selectedNode={selectedNode}
                  tempInputValues={tempInputValues}
                  onUpdateProperty={updateNodeProperty}
                />
              ),
              disabled: selectedNode.type === GameNodeType.CAMERA
            },
            // 摄像机属性标签页 - 仅对摄像机节点显示
            {
              id: 'camera',
              label: '摄像机属性',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              ),
              content: (
                <CameraPropertiesTab
                  selectedNode={selectedNode as CameraNodeProperties}
                  tempInputValues={tempInputValues}
                  onUpdateProperty={updateNodeProperty}
                />
              ),
              disabled: selectedNode.type !== GameNodeType.CAMERA
            },
            {
              id: 'model-animation',
              label: '模型动画',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              ),
              content: (
                <ModelAnimationTab
                  selectedNode={selectedNode}
                  isDragOver={isDragOver}
                  isDragOverAnimation={isDragOverAnimation}
                  isUploadingModel={isUploadingModel}
                  isUploadingAnimation={isUploadingAnimation}
                  uploadError={uploadError}
                  animationError={animationError}
                  onSetIsDragOver={setIsDragOver}
                  onSetIsDragOverAnimation={setIsDragOverAnimation}
                  onModelUpload={handleModelUpload}
                  onAnimationUpload={handleAnimationUpload}
                  onPreviewAnimation={previewAnimation}
                  onStopPreview={stopPreviewAnimation}
                  onSetDefaultAnimation={setDefaultAnimation}
                  onRemoveAnimation={removeAnimation}
                  onPreviewModel={previewModel}
                  onSetDefaultModel={setDefaultModel}
                  onRemoveModel={removeModelHandler}
                />
              ),
              disabled: selectedNode.type !== GameNodeType.MESH
            },
            {
              id: 'material',
              label: '材质管理',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                </svg>
              ),
              content: (
                <MaterialTab
                  selectedNode={selectedNode}
                  isDragOverMaterial={isDragOverMaterial}
                  isUploadingMaterial={isUploadingMaterial}
                  materialError={materialError}
                  onSetIsDragOverMaterial={setIsDragOverMaterial}
                  onMaterialUpload={handleMaterialUpload}
                  onPreviewMaterial={previewMaterial}
                  onSetDefaultMaterial={setDefaultMaterial}
                  onRemoveMaterial={removeMaterialHandler}
                />
              ),
              disabled: selectedNode.type !== GameNodeType.MESH
            }
          ]}
          defaultActiveTab="basic"
          className="h-full"
        />
      </div>

      {/* 保存按钮 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        {/* 保存状态消息 */}
        {saveMessage && (
          <div className={`mb-3 p-2 rounded text-sm ${
            saveMessage.includes('成功')
              ? 'bg-green-100 text-green-800 border border-green-300'
              : 'bg-red-100 text-red-800 border border-red-300'
          }`}>
            {saveMessage}
          </div>
        )}

        <button
          onClick={saveNodeConfiguration}
          disabled={isSaving}
          className={`w-full px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2 ${
            isSaving
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isSaving ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>保存中...</span>
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
              </svg>
              <span>保存配置</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default NodePropertiesPanel;