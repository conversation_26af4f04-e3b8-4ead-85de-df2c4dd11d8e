/**
 * 代码转换服务
 * 负责将TSX组件代码转换为独立的HTML文件
 * 确保Babylon.js API调用的完全一致性
 */
export class CodeTransformationService {
  
  /**
   * 将TSX组件转换为HTML文件
   */
  async transformTSXToHTML(tsxCode: string, componentName: string): Promise<{
    html: string;
    css: string;
    javascript: string;
  }> {
    console.log(`[CodeTransformationService] 开始转换TSX组件: ${componentName}`);
    
    // 1. 解析TSX代码结构
    const parsedCode = this.parseTSXCode(tsxCode);
    
    // 2. 转换Babylon.js API调用
    const transformedJS = this.transformBabylonJSAPIs(parsedCode.javascript);
    
    // 3. 提取和转换样式
    const transformedCSS = this.extractAndTransformStyles(parsedCode.styles);
    
    // 4. 生成HTML结构
    const html = this.generateHTMLStructure(transformedJS, transformedCSS, componentName);
    
    console.log(`[CodeTransformationService] 转换完成: HTML(${html.length}), CSS(${transformedCSS.length}), JS(${transformedJS.length})`);
    
    return {
      html,
      css: transformedCSS,
      javascript: transformedJS
    };
  }

  /**
   * 解析TSX代码结构
   */
  private parseTSXCode(tsxCode: string): {
    imports: string[];
    javascript: string;
    styles: string;
    jsx: string;
  } {
    const result = {
      imports: [] as string[],
      javascript: '',
      styles: '',
      jsx: ''
    };

    // 提取import语句
    const importMatches = tsxCode.match(/import\s+.*?from\s+['"][^'"]+['"];?/g);
    if (importMatches) {
      result.imports = importMatches;
    }

    // 提取JavaScript逻辑（函数体内容）
    const functionBodyMatch = tsxCode.match(/const\s+\w+.*?=.*?\{([\s\S]*?)return\s*\(/);
    if (functionBodyMatch) {
      result.javascript = functionBodyMatch[1];
    }

    // 提取JSX结构
    const jsxMatch = tsxCode.match(/return\s*\(([\s\S]*?)\);?\s*\}?\s*$/);
    if (jsxMatch) {
      result.jsx = jsxMatch[1];
    }

    return result;
  }

  /**
   * 转换Babylon.js API调用（已废弃，保留用于兼容性）
   * 将ES6模块导入转换为全局BABYLON命名空间调用
   */
  private transformBabylonJSAPIs(jsCode: string): string {
    let transformed = jsCode;

    // API转换映射表
    const apiTransformations = [
      // 引擎和场景
      { from: /new Engine\(/g, to: 'new BABYLON.Engine(' },
      { from: /new Scene\(/g, to: 'new BABYLON.Scene(' },
      
      // 摄像机
      { from: /new ArcRotateCamera\(/g, to: 'new BABYLON.ArcRotateCamera(' },
      { from: /new FreeCamera\(/g, to: 'new BABYLON.FreeCamera(' },
      { from: /new UniversalCamera\(/g, to: 'new BABYLON.UniversalCamera(' },
      
      // 光照
      { from: /new HemisphericLight\(/g, to: 'new BABYLON.HemisphericLight(' },
      { from: /new DirectionalLight\(/g, to: 'new BABYLON.DirectionalLight(' },
      { from: /new PointLight\(/g, to: 'new BABYLON.PointLight(' },
      
      // 网格和几何体
      { from: /MeshBuilder\.Create/g, to: 'BABYLON.MeshBuilder.Create' },
      { from: /new StandardMaterial\(/g, to: 'new BABYLON.StandardMaterial(' },
      { from: /new PBRMaterial\(/g, to: 'new BABYLON.PBRMaterial(' },
      { from: /new Texture\(/g, to: 'new BABYLON.Texture(' },
      
      // 物理引擎（V2 API）
      { from: /new HavokPlugin\(/g, to: 'new BABYLON.HavokPlugin(' },
      { from: /new PhysicsAggregate\(/g, to: 'new BABYLON.PhysicsAggregate(' },
      { from: /PhysicsShapeType\./g, to: 'BABYLON.PhysicsShapeType.' },
      { from: /PhysicsMotionType\./g, to: 'BABYLON.PhysicsMotionType.' },
      
      // 动画和粒子
      { from: /new Animation\(/g, to: 'new BABYLON.Animation(' },
      { from: /new ParticleSystem\(/g, to: 'new BABYLON.ParticleSystem(' },
      { from: /Animation\.Create/g, to: 'BABYLON.Animation.Create' },
      
      // 输入和交互
      { from: /new ActionManager\(/g, to: 'new BABYLON.ActionManager(' },
      { from: /ActionManager\./g, to: 'BABYLON.ActionManager.' },
      { from: /ExecuteCodeAction\(/g, to: 'BABYLON.ExecuteCodeAction(' },
      
      // 向量和数学
      { from: /new Vector3\(/g, to: 'new BABYLON.Vector3(' },
      { from: /new Color3\(/g, to: 'new BABYLON.Color3(' },
      { from: /Vector3\./g, to: 'BABYLON.Vector3.' },
      { from: /Color3\./g, to: 'BABYLON.Color3.' },
      
      // 工具和调试
      { from: /new ShadowGenerator\(/g, to: 'new BABYLON.ShadowGenerator(' },
      { from: /Debug\./g, to: 'BABYLON.Debug.' }
    ];

    // 应用所有转换
    apiTransformations.forEach(({ from, to }) => {
      transformed = transformed.replace(from, to);
    });

    // 移除import语句相关的变量声明
    transformed = this.removeImportRelatedCode(transformed);

    return transformed;
  }

  /**
   * 移除与import相关的代码
   */
  private removeImportRelatedCode(jsCode: string): string {
    let cleaned = jsCode;

    // 移除解构赋值声明（通常来自import）
    cleaned = cleaned.replace(/const\s*\{[^}]+\}\s*=\s*[^;]+;?\s*/g, '');
    
    // 移除单个变量声明（如果明显来自import）
    cleaned = cleaned.replace(/const\s+\w+\s*=\s*BABYLON\.\w+;?\s*/g, '');

    return cleaned;
  }

  /**
   * 提取和转换样式
   */
  private extractAndTransformStyles(stylesCode: string): string {
    // 基础游戏样式模板
    const baseStyles = `
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: #000;
  overflow: hidden;
  touch-action: none;
}

#gameContainer {
  position: relative;
  width: 100vw;
  height: 100vh;
}

#renderCanvas {
  width: 100%;
  height: 100%;
  display: block;
  outline: none;
}

.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.game-ui > * {
  pointer-events: auto;
}

.score-display {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.control-button {
  background: rgba(255,255,255,0.2);
  border: 2px solid rgba(255,255,255,0.5);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: 5px;
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.2s;
}

.control-button:hover {
  background: rgba(255,255,255,0.3);
  border-color: rgba(255,255,255,0.8);
}

.control-button:active {
  transform: scale(0.95);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .score-display {
    font-size: 18px;
    top: 10px;
    left: 10px;
  }
  
  .controls {
    bottom: 10px;
    right: 10px;
  }
  
  .control-button {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .score-display {
    font-size: 16px;
    top: 5px;
    left: 5px;
  }
  
  .controls {
    bottom: 5px;
    right: 5px;
  }
}`;

    return baseStyles + (stylesCode || '');
  }

  /**
   * 生成HTML结构
   */
  private generateHTMLStructure(jsCode: string, cssCode: string, componentName: string): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>${componentName} - PlayableGen Game</title>
    <style>
${cssCode}
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="renderCanvas"></canvas>
        <div class="game-ui">
            <div class="score-display" id="scoreDisplay">分数: 0</div>
            <div class="controls" id="gameControls">
                <!-- 游戏控制按钮将由JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- Babylon.js CDN -->
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <script src="https://cdn.babylonjs.com/materialsLibrary/babylonjs.materials.min.js"></script>
    <script src="https://cdn.babylonjs.com/postProcessesLibrary/babylonjs.postProcess.min.js"></script>
    <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.min.js"></script>
    <script src="https://cdn.babylonjs.com/serializers/babylonjs.serializers.min.js"></script>
    <script src="https://cdn.babylonjs.com/gui/babylon.gui.min.js"></script>

    <script>
        // 游戏主逻辑
        class ${componentName}Game {
            constructor() {
                this.canvas = document.getElementById('renderCanvas');
                this.engine = null;
                this.scene = null;
                this.score = 0;
                this.init();
            }

            async init() {
                try {
                    // 初始化Babylon.js引擎
                    this.engine = new BABYLON.Engine(this.canvas, true, {
                        preserveDrawingBuffer: true,
                        stencil: true,
                        antialias: true
                    });

                    // 创建场景
                    this.scene = new BABYLON.Scene(this.engine);
                    
                    // 执行游戏逻辑
                    await this.createGame();
                    
                    // 启动渲染循环
                    this.engine.runRenderLoop(() => {
                        this.scene.render();
                    });

                    // 处理窗口大小变化
                    window.addEventListener('resize', () => {
                        this.engine.resize();
                    });

                    console.log('${componentName} 游戏初始化完成');
                } catch (error) {
                    console.error('游戏初始化失败:', error);
                }
            }

            async createGame() {
                // 转换后的游戏逻辑
${jsCode.split('\n').map(line => '                ' + line).join('\n')}
            }

            updateScore(newScore) {
                this.score = newScore;
                const scoreDisplay = document.getElementById('scoreDisplay');
                if (scoreDisplay) {
                    scoreDisplay.textContent = \`分数: \${this.score}\`;
                }
            }

            dispose() {
                if (this.scene) {
                    this.scene.dispose();
                }
                if (this.engine) {
                    this.engine.dispose();
                }
            }
        }

        // 启动游戏
        window.addEventListener('DOMContentLoaded', () => {
            const game = new ${componentName}Game();
            
            // 全局游戏实例，便于调试
            window.game = game;
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (window.game) {
                window.game.dispose();
            }
        });
    </script>
</body>
</html>`;
  }

  /**
   * 验证转换结果的API一致性
   */
  validateAPIConsistency(originalTSX: string, transformedJS: string): {
    isConsistent: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // 检查关键API调用是否正确转换
    const criticalAPIs = [
      'Engine',
      'Scene',
      'Camera',
      'Light',
      'MeshBuilder',
      'PhysicsAggregate',
      'HavokPlugin'
    ];

    criticalAPIs.forEach(api => {
      const tsxPattern = new RegExp(`new ${api}\\(`, 'g');
      const htmlPattern = new RegExp(`new BABYLON\\.${api}\\(`, 'g');
      
      const tsxMatches = (originalTSX.match(tsxPattern) || []).length;
      const htmlMatches = (transformedJS.match(htmlPattern) || []).length;
      
      if (tsxMatches !== htmlMatches) {
        issues.push(`${api} API调用数量不匹配: TSX(${tsxMatches}) vs HTML(${htmlMatches})`);
      }
    });

    return {
      isConsistent: issues.length === 0,
      issues
    };
  }
}
