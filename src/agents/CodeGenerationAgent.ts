/**
 * 重构后的代码生成Agent - 专注于Three.js游戏开发
 * 模块化设计，使用最新的Three.js最佳实践
 */

import { BaseAgent, AgentState, AgentConfig } from "./BaseAgent";
import { GameDesign } from "./GameDesignAgent";

// 导入模块化组件
import { ThreeJSBestPractices } from './prompts/ThreeJSBestPractices';
import { CodeGenerationPrompts } from './prompts/CodeGenerationPrompts';
import { CodeGenerationConfigManager, CodeGenerationConfig, GenerationResult } from './config/CodeGenerationConfig';
import { CodeQualityChecker, QualityReport } from './quality/CodeQualityChecker';

// 导出类型
export type { CodeGenerationConfig } from './config/CodeGenerationConfig';

// 生成的代码结构
export interface GeneratedCode {
  component: string;
  fileName: string;
  dependencies: string[];
  assets: {
    textures: string[];
    models: string[];
    sounds: string[];
    other: string[];
  };
  metadata: {
    componentName: string;
    gameType: string;
    description: string;
    qualityScore: number;
  };
}

// 脚本文件接口（保持兼容性）
export interface GeneratedScript {
  id: string;
  name: string;
  content: string;
  nodeId?: string;
  createdAt: Date;
  lastModified: Date;
  isActive: boolean;
  metadata: {
    description: string;
    targetNodeTypes: string[];
  };
}

// 场景上下文接口（保持兼容性）
export interface SceneContext {
  nodes: Array<{
    id: string;
    name: string;
    type: string;
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    scaling: { x: number; y: number; z: number };
  }>;
  selectedNode: {
    id: string;
    name: string;
    type: string;
  } | null;
  availableAssets: {
    models: Array<{ name: string; path: string }>;
    textures: Array<{ name: string; path: string }>;
  };
}

/**
 * 重构后的代码生成Agent
 * 使用模块化架构和Three.js最佳实践
 */
export class CodeGenerationAgent extends BaseAgent {
  private configManager: CodeGenerationConfigManager;
  private qualityChecker: CodeQualityChecker;
  constructor(config?: Partial<AgentConfig & { generationConfig: CodeGenerationConfig }>) {
    const defaultConfig: AgentConfig = {
      name: "CodeGenerator",
      role: "程序媛",
      systemPrompt: CodeGenerationPrompts.buildSystemPrompt(),
      temperature: config?.generationConfig?.temperature || 0.1,
      ...config
    };

    super(defaultConfig);

    this.configManager = CodeGenerationConfigManager.getInstance();
    this.qualityChecker = new CodeQualityChecker();

    // 如果提供了生成配置，更新配置管理器
    if (config?.generationConfig) {
      this.configManager.updateConfig(config.generationConfig);
    }
  }

  /**
   * 实现BaseAgent的抽象方法
   */
  protected async executeNode(state: AgentState): Promise<Partial<AgentState>> {
    try {
      // 从状态中提取游戏设计信息
      const gameDesign = state.context?.gameDesign;
      if (!gameDesign) {
        throw new Error('No game design found in state');
      }

      // 生成代码
      const generatedCode = await this.generateGameCode(gameDesign);

      return {
        context: {
          ...state.context,
          generatedCode
        },
        status: 'completed'
      };
    } catch (error) {
      return {
        context: {
          ...state.context,
          error: error instanceof Error ? error.message : String(error)
        },
        status: 'error'
      };
    }
  }

  /**
   * 生成游戏代码（主要方法）
   */
  async generateGameCode(gameDesign: GameDesign): Promise<GeneratedCode> {
    console.log('[CodeGenerationAgent] 开始生成Three.js游戏代码...');

    try {
      // 第一步：分析需要的API主题
      const requiredTopics = ThreeJSBestPractices.analyzeRequiredTopics(gameDesign, '');
      console.log('[CodeGenerationAgent] 分析所需API主题:', requiredTopics);

      // 第二步：获取Three.js最佳实践
      const bestPractices = ThreeJSBestPractices.getBestPractices(requiredTopics);

      // 第三步：构建完整的生成提示
      const codePrompt = CodeGenerationPrompts.buildCodePrompt(gameDesign, bestPractices);

      // 第四步：生成代码
      const generationResult = await this.generateCodeWithRetry(codePrompt, 3);

      if (!generationResult.success || !generationResult.code) {
        throw new Error(generationResult.error || '代码生成失败');
      }

      // 第五步：质量检查
      const qualityReport = this.qualityChecker.checkQuality(generationResult.code);
      console.log('[CodeGenerationAgent] 代码质量分数:', qualityReport.score);

      // 第六步：如果质量分数过低，尝试优化
      let finalCode = generationResult.code;
      if (qualityReport.score < 70 && qualityReport.issues.length > 0) {
        console.log('[CodeGenerationAgent] 质量分数过低，尝试优化代码...');
        const optimizedResult = await this.optimizeCode(finalCode, qualityReport);
        if (optimizedResult.success && optimizedResult.code) {
          finalCode = optimizedResult.code;
        }
      }

      // 第七步：解析并返回结果
      const parsedCode = this.parseGeneratedCode(finalCode, gameDesign, qualityReport.score);

      console.log('[CodeGenerationAgent] 代码生成完成');
      return parsedCode;

    } catch (error) {
      console.error('[CodeGenerationAgent] 代码生成失败:', error);
      throw error;
    }
  }

  /**
   * 调用模型生成响应
   */
  private async invokeModel(prompt: string): Promise<string> {
    try {
      const messages = [
        { role: 'system' as const, content: this.systemPrompt },
        { role: 'user' as const, content: prompt }
      ];

      const response = await this.model.invoke(messages);
      return response.content as string;
    } catch (error) {
      console.error('[CodeGenerationAgent] 模型调用失败:', error);
      throw error;
    }
  }

  /**
   * 带重试的代码生成
   */
  private async generateCodeWithRetry(prompt: string, maxRetries: number): Promise<GenerationResult> {
    let lastError: string = '';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[CodeGenerationAgent] 尝试生成代码 (${attempt}/${maxRetries})`);

        const response = await this.invokeModel(prompt);

        if (response && response.trim()) {
          return {
            success: true,
            code: response.trim(),
            metadata: {
              tokensUsed: response.length / 4, // 粗略估算
              generationTime: Date.now(),
              codeLines: response.split('\n').length,
              dependencies: this.extractDependencies(response)
            }
          };
        } else {
          lastError = '生成的代码为空';
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error);
        console.warn(`[CodeGenerationAgent] 第${attempt}次尝试失败:`, lastError);

        if (attempt < maxRetries) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return {
      success: false,
      error: `所有${maxRetries}次尝试都失败了。最后错误: ${lastError}`
    };
  }

  /**
   * 优化代码质量
   */
  private async optimizeCode(code: string, qualityReport: QualityReport): Promise<GenerationResult> {
    try {
      const issues = qualityReport.issues.map(issue =>
        `${issue.type.toUpperCase()}: ${issue.message} (Line ${issue.line || 'unknown'})`
      );

      const optimizationPrompt = CodeGenerationPrompts.buildOptimizationPrompt(code, issues);
      const response = await this.invokeModel(optimizationPrompt);

      if (response && response.trim()) {
        return {
          success: true,
          code: response.trim()
        };
      } else {
        return {
          success: false,
          error: '优化后的代码为空'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 生成代码（通用方法）
   */
  async generateCode(prompt: string, additionalContext?: string): Promise<string> {
    try {
      const fullPrompt = `${prompt}\n\n${additionalContext || ''}`;
      const response = await this.invokeModel(fullPrompt);
      return response || '';
    } catch (error) {
      console.error('[CodeGenerationAgent] 代码生成失败:', error);
      throw error;
    }
  }

  /**
   * 提取代码中的依赖
   */
  private extractDependencies(code: string): string[] {
    const deps = new Set<string>();

    // 基础依赖
    deps.add('react');
    deps.add('@types/react');
    deps.add('typescript');
    deps.add('three');
    deps.add('@types/three');

    // 从import语句中提取
    const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
    let match;
    while ((match = importRegex.exec(code)) !== null) {
      const importPath = match[1];
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        deps.add(importPath);
      }
    }

    return Array.from(deps);
  }

  /**
   * 解析生成的代码
   */
  private parseGeneratedCode(content: string, gameDesign: GameDesign, qualityScore: number): GeneratedCode {
    // 清理代码内容
    let componentCode = content.trim();

    // 去除markdown代码块标记
    if (componentCode.startsWith('```typescript') || componentCode.startsWith('```tsx') || componentCode.startsWith('```')) {
      const lines = componentCode.split('\n');
      lines.shift(); // 移除开头的```
      if (lines[lines.length - 1].trim() === '```') {
        lines.pop(); // 移除结尾的```
      }
      componentCode = lines.join('\n');
    }

    // 提取依赖
    const dependencies = this.extractDependencies(componentCode);

    // 提取资源
    const assets = this.extractAssets(componentCode);

    // 生成文件名
    const fileName = this.generateFileName(gameDesign);

    // 提取组件名
    const componentName = this.extractComponentName(componentCode) || 'ThreeJSGame';

    return {
      component: componentCode,
      fileName,
      dependencies,
      assets,
      metadata: {
        componentName,
        gameType: gameDesign.gameType || 'unknown',
        description: gameDesign.theme || '3D游戏组件',
        qualityScore
      }
    };
  }

  /**
   * 提取资源文件
   */
  private extractAssets(code: string): GeneratedCode['assets'] {
    const assets = {
      textures: [] as string[],
      models: [] as string[],
      sounds: [] as string[],
      other: [] as string[]
    };

    // 提取纹理文件
    const textureRegex = /['"]([^'"]*\.(jpg|jpeg|png|gif|bmp|webp))['"/]/gi;
    let match;
    while ((match = textureRegex.exec(code)) !== null) {
      assets.textures.push(match[1]);
    }

    // 提取模型文件（FBX优先）
    const modelRegex = /['"]([^'"]*\.(fbx|glb|gltf|obj))['"/]/gi;
    while ((match = modelRegex.exec(code)) !== null) {
      assets.models.push(match[1]);
    }

    // 提取音频文件
    const audioRegex = /['"]([^'"]*\.(mp3|wav|ogg|m4a))['"/]/gi;
    while ((match = audioRegex.exec(code)) !== null) {
      assets.sounds.push(match[1]);
    }

    return assets;
  }

  /**
   * 生成文件名
   */
  private generateFileName(gameDesign: GameDesign): string {
    const baseName = gameDesign.gameType || 'ThreeJSGame';
    const sanitized = baseName.replace(/[^a-zA-Z0-9]/g, '');
    const capitalized = sanitized.charAt(0).toUpperCase() + sanitized.slice(1);
    return `${capitalized}Component.tsx`;
  }

  /**
   * 提取组件名
   */
  private extractComponentName(code: string): string | null {
    const componentRegex = /(?:export\s+default\s+function\s+(\w+)|function\s+(\w+)|const\s+(\w+)\s*=.*?=>\s*{)/;
    const match = code.match(componentRegex);
    return match ? (match[1] || match[2] || match[3]) : null;
  }

  /**
   * 获取依赖列表
   */
  getDependencies(): string[] {
    return this.configManager.getThreeJSDependencies();
  }

  /**
   * 更新生成配置
   */
  updateConfig(config: Partial<CodeGenerationConfig>): void {
    this.configManager.updateConfig(config);
  }

  /**
   * 获取当前配置
   */
  getConfig(): CodeGenerationConfig {
    return this.configManager.getConfig();
  }

  /**
   * 获取代码质量报告
   */
  getQualityReport(code: string): QualityReport {
    return this.qualityChecker.checkQuality(code);
  }

  // ==================== 脚本生成功能（兼容性方法） ====================

  /**
   * 生成脚本代码
   */
  async generateScript(
    userRequirement: string,
    sceneContext?: SceneContext,
    selectedNodeId?: string
  ): Promise<GeneratedScript> {
    try {
      const prompt = this.buildScriptPrompt(userRequirement, sceneContext);
      const response = await this.invokeModel(prompt);

      return {
        id: `script_${Date.now()}`,
        name: this.extractScriptName(userRequirement),
        content: response || '',
        nodeId: selectedNodeId,
        createdAt: new Date(),
        lastModified: new Date(),
        isActive: true,
        metadata: {
          description: userRequirement,
          targetNodeTypes: sceneContext?.selectedNode ? [sceneContext.selectedNode.type] : []
        }
      };
    } catch (error) {
      console.error('[CodeGenerationAgent] 脚本生成失败:', error);
      throw error;
    }
  }

  /**
   * 构建脚本生成提示
   */
  private buildScriptPrompt(
    userRequirement: string,
    sceneContext?: SceneContext
  ): string {
    let prompt = `请根据以下要求生成简洁高效的Three.js脚本代码：

## 用户需求
${userRequirement}

## 技术要求
- 使用Three.js最新版本
- 生成简洁的JavaScript代码（不是TypeScript）
- 创建一个executeScript函数作为入口点
- 使用传入的scene、camera、renderer等Three.js对象
- 确保代码可以直接在浏览器中运行

## 代码结构要求
\`\`\`javascript
function executeScript(scene, camera, renderer) {
  // 你的Three.js代码逻辑
  // 例如：创建几何体、材质、动画、交互等

  // 如果需要清理资源，返回清理函数
  return function cleanup() {
    // 清理代码（如果需要）
  };
}
\`\`\`

## 编码原则
- 保持代码简洁明了，避免过度复杂的实现
- 优先使用Three.js内置的几何体、材质和功能
- 对于动画，使用requestAnimationFrame进行循环
- 合理使用Three.js的最佳实践
- 避免不必要的功能或复杂的类结构

## 重要提示
- 请生成最简洁有效的代码，避免过度工程化
- 根据需求复杂度控制代码长度，简单需求保持简洁
- 确保代码具有良好的可读性和可维护性

`;

    if (sceneContext && sceneContext.nodes.length > 0) {
      prompt += `## 场景上下文
当前场景包含 ${sceneContext.nodes.length} 个节点：
`;

      // 列出所有节点
      sceneContext.nodes.forEach((node: any) => {
        prompt += `- ${node.name} (${node.type}) - ID: ${node.id}`;
        if (node.position) {
          prompt += `
  位置: (${node.position.x}, ${node.position.y}, ${node.position.z})`;
        }
        if (node.rotation) {
          prompt += `
  旋转: (${node.rotation.x}, ${node.rotation.y}, ${node.rotation.z})`;
        }
        prompt += `\n`;
      });

      if (sceneContext.selectedNode) {
        const selectedNode = sceneContext.selectedNode as any;
        prompt += `
**当前选中节点**: ${selectedNode.name} (${selectedNode.type})
- ID: ${selectedNode.id}`;
        if (selectedNode.position) {
          prompt += `
- 位置: (${selectedNode.position.x}, ${selectedNode.position.y}, ${selectedNode.position.z})`;
        }
        if (selectedNode.rotation) {
          prompt += `
- 旋转: (${selectedNode.rotation.x}, ${selectedNode.rotation.y}, ${selectedNode.rotation.z})`;
        }
        prompt += `\n`;
      }

      prompt += `
## 重要提示
- 如果用户提到"主角"、"角色"、"当前节点"等，请优先操作现有节点，不要创建新节点
- 使用 scene.getObjectByName('节点ID') 来获取现有节点
- 如果找不到指定节点，再考虑创建新节点
- 优先使用场景中已存在的节点进行操作
`;
    }

    prompt += `
请直接输出可执行的JavaScript代码，无需markdown标记。确保代码包含executeScript函数，并保持代码简洁高效。`;

    return prompt;
  }

  /**
   * 提取脚本名称
   */
  private extractScriptName(userRequirement: string): string {
    // 简单的名称提取逻辑
    const words = userRequirement.split(' ').slice(0, 3);
    return words.join('_').replace(/[^a-zA-Z0-9_]/g, '') || 'CustomScript';
  }

  /**
   * 流式生成脚本代码
   */
  async *generateScriptStream(
    userRequirement: string,
    sceneContext?: SceneContext
  ): AsyncGenerator<{ type: 'token' | 'complete', data: string }, void, unknown> {
    const prompt = this.buildScriptPrompt(userRequirement, sceneContext);
    console.log('[CodeGenerationAgent] 开始流式生成脚本');

    let accumulatedContent = '';
    let isComplete = false;
    let error: Error | null = null;

    // 创建一个Promise来处理流式响应
    const streamPromise = new Promise<string>((resolve, reject) => {
      const streamCallback = {
        onToken: (token: string) => {
          accumulatedContent += token;
          // 这里不能直接yield，因为我们在回调中
        },
        onStatus: () => {
          // 状态更新，不需要日志
        }
      };

      // 调用流式API
      this.model.invokeStream(
        [{ role: 'user', content: prompt }],
        streamCallback
      ).then(response => {
        const finalContent = typeof response.content === 'string' ? response.content : accumulatedContent;
        resolve(finalContent);
      }).catch(err => {
        reject(err);
      });
    });

    // 启动流式调用
    streamPromise.then(() => {
      isComplete = true;
    }).catch(err => {
      error = err;
      isComplete = true;
    });

    // 监控accumulatedContent的变化并实时yield
    let lastLength = 0;
    while (!isComplete) {
      if (accumulatedContent.length > lastLength) {
        const newContent = accumulatedContent.slice(lastLength);
        lastLength = accumulatedContent.length;
        yield { type: 'token', data: newContent };
      }
      // 短暂等待
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    // 处理最后的内容
    if (accumulatedContent.length > lastLength) {
      const newContent = accumulatedContent.slice(lastLength);
      yield { type: 'token', data: newContent };
    }

    if (error) {
      console.error('[CodeGenerationAgent] 流式生成失败，回退到普通模式');
      // 回退到普通生成
      const response = this.invokeModel(prompt);
      const content = response || '';

      if (content && typeof content === 'string') {
        // 快速分块输出
        for (let i = 0; i < content.length; i += 20) {
          const chunk = content.slice(i, i + 20);
          yield { type: 'token', data: chunk };
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        yield { type: 'complete', data: content };
      }
    } else {
      // 等待最终结果
      const finalContent = await streamPromise;
      yield { type: 'complete', data: finalContent };
    }
  }

  // ==================== 兼容性方法 ====================

  /**
   * 流式生成代码（兼容性方法）
   */
  async *generateCodeStream(gameDesign: GameDesign): AsyncGenerator<string, void, unknown> {
    try {
      const result = await this.generateGameCode(gameDesign);
      // 模拟流式输出
      const lines = result.component.split('\n');
      for (const line of lines) {
        yield line + '\n';
        // 添加小延迟以模拟流式效果
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    } catch (error) {
      console.error('[CodeGenerationAgent] 流式生成失败:', error);
      throw error;
    }
  }

  /**
   * 验证生成的代码
   */
  validateGeneratedCode(code: string): { valid: boolean; errors: string[] } {
    const qualityReport = this.getQualityReport(code);
    const errors = qualityReport.issues
      .filter(issue => issue.type === 'error')
      .map(issue => issue.message);

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取生成统计信息
   */
  getGenerationStats() {
    return this.configManager.getGenerationStats();
  }
}