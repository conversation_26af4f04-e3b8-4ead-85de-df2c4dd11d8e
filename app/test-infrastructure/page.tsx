"use client";

import { Suspense } from "react";
import ThreeCanvas from "../../src/three/components/ThreeCanvas";

/**
 * 基础设施测试页面
 * 展示标准化的Three.js基础设施功能
 */
export default function TestInfrastructurePage() {
  return (
    <div className="min-h-screen bg-gray-900">
      {/* 页面标题 */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold text-white mb-2">
            PlayableGen 基础设施测试
          </h1>
          <p className="text-gray-300 text-sm">
            测试标准化的 ThreeEngine、BaseThreeScene 和 ThreeUtils 组件
          </p>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-120px)]">
          
          {/* 3D场景区域 */}
          <div className="lg:col-span-3">
            <div className="bg-gray-800 rounded-lg overflow-hidden h-full">
              <div className="bg-gray-700 px-4 py-2 border-b border-gray-600">
                <h2 className="text-white font-semibold">3D 测试场景</h2>
                <p className="text-gray-300 text-sm">
                  基于 BaseGameScene 的标准化游戏场景
                </p>
              </div>
              <div className="h-[calc(100%-60px)]">
                <Suspense fallback={
                  <div className="flex items-center justify-center h-full bg-gray-900">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                      <p className="text-white">加载 Babylon.js 引擎...</p>
                    </div>
                  </div>
                }>
                  <TestGameCanvas className="w-full h-full" />
                </Suspense>
              </div>
            </div>
          </div>

          {/* 信息面板 */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-lg h-full overflow-y-auto">
              <div className="bg-gray-700 px-4 py-2 border-b border-gray-600">
                <h2 className="text-white font-semibold">技术架构</h2>
              </div>
              <div className="p-4 space-y-6">
                
                {/* 核心组件 */}
                <div>
                  <h3 className="text-white font-medium mb-3">核心组件</h3>
                  <div className="space-y-2 text-sm">
                    <div className="bg-green-900 bg-opacity-50 p-2 rounded border-l-2 border-green-500">
                      <div className="text-green-300 font-medium">BabylonEngineManager</div>
                      <div className="text-gray-300">单例引擎管理器</div>
                    </div>
                    <div className="bg-blue-900 bg-opacity-50 p-2 rounded border-l-2 border-blue-500">
                      <div className="text-blue-300 font-medium">BaseGameScene</div>
                      <div className="text-gray-300">游戏场景基类</div>
                    </div>
                    <div className="bg-purple-900 bg-opacity-50 p-2 rounded border-l-2 border-purple-500">
                      <div className="text-purple-300 font-medium">BabylonUtils</div>
                      <div className="text-gray-300">通用工具库</div>
                    </div>
                  </div>
                </div>

                {/* 功能特性 */}
                <div>
                  <h3 className="text-white font-medium mb-3">功能特性</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      标准化引擎配置
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      自动窗口大小调整
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      性能监控系统
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Inspector 集成
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      生命周期管理
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      事件系统
                    </div>
                  </div>
                </div>

                {/* 技术规格 */}
                <div>
                  <h3 className="text-white font-medium mb-3">技术规格</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div>
                      <span className="text-gray-400">Babylon.js:</span> v7.42.0
                    </div>
                    <div>
                      <span className="text-gray-400">渲染器:</span> WebGL 2.0
                    </div>
                    <div>
                      <span className="text-gray-400">抗锯齿:</span> 启用
                    </div>
                    <div>
                      <span className="text-gray-400">设备适配:</span> 自动
                    </div>
                    <div>
                      <span className="text-gray-400">调试模式:</span> 开发环境
                    </div>
                  </div>
                </div>

                {/* 测试场景内容 */}
                <div>
                  <h3 className="text-white font-medium mb-3">测试场景</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div>• 旋转立方体（红色）</div>
                    <div>• 缩放球体（蓝色）</div>
                    <div>• 静态地面（绿色）</div>
                    <div>• 标准光照系统</div>
                    <div>• 弧形旋转相机</div>
                    <div>• 动画系统</div>
                    <div>• 交互控制</div>
                  </div>
                </div>

                {/* 开发说明 */}
                <div>
                  <h3 className="text-white font-medium mb-3">开发说明</h3>
                  <div className="text-xs text-gray-400 space-y-1">
                    <p>
                      此测试页面展示了 PlayableGen 平台的标准化基础设施。
                      所有组件都基于 Babylon.js 7.42 最新 API 实现。
                    </p>
                    <p className="mt-2">
                      左侧控制面板提供了完整的游戏状态监控、性能指标显示和交互控制功能。
                    </p>
                  </div>
                </div>

              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}
