/**
 * CheckpointTestPanel - Checkpoint系统测试面板
 * 用于测试和验证checkpoint功能
 */

'use client';

import React, { useState } from 'react';
import { useNodeSystem } from '../contexts/NodeSystemContext';

interface CheckpointTestPanelProps {
  className?: string;
}

export const CheckpointTestPanel: React.FC<CheckpointTestPanelProps> = ({ className = '' }) => {
  const {
    checkpoints,
    currentCheckpointId,
    isCreatingCheckpoint,
    isRollingBack,
    checkpointError,
    createCheckpoint,
    rollbackToCheckpoint,
    loadCheckpoints,
    recoverFromError
  } = useNodeSystem();

  const [testMessage, setTestMessage] = useState('');

  // 测试创建checkpoint
  const handleTestCreate = async () => {
    if (!testMessage.trim()) {
      alert('请输入测试消息');
      return;
    }

    const messageId = `test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const result = await createCheckpoint(
      messageId,
      testMessage,
      `测试创建：${testMessage}`,
      []
    );

    if (result) {
      alert(`Checkpoint创建成功：${result.version}`);
      setTestMessage('');
    } else {
      alert('Checkpoint创建失败');
    }
  };

  // 测试回滚
  const handleTestRollback = async (checkpointId: string) => {
    const confirmed = confirm('确定要回滚到此版本吗？这将清除后续所有变更。');
    if (!confirmed) return;

    const success = await rollbackToCheckpoint(checkpointId);
    if (success) {
      alert('回滚成功');
    } else {
      alert('回滚失败');
    }
  };

  // 测试错误恢复
  const handleTestRecover = async () => {
    await recoverFromError();
    alert('错误恢复完成');
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Checkpoint系统测试</h3>

      {/* 错误显示 */}
      {checkpointError && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
          <p className="text-red-800 text-sm">
            <strong>错误：</strong>{checkpointError}
          </p>
          <button
            onClick={handleTestRecover}
            className="mt-2 px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
          >
            尝试恢复
          </button>
        </div>
      )}

      {/* 状态显示 */}
      <div className="mb-4 p-3 bg-gray-50 rounded">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Checkpoint总数：</span>
            <span>{checkpoints.length}</span>
          </div>
          <div>
            <span className="font-medium">当前版本：</span>
            <span>{currentCheckpointId ? 
              checkpoints.find(cp => cp.id === currentCheckpointId)?.version || '未知' 
              : '无'
            }</span>
          </div>
          <div>
            <span className="font-medium">创建状态：</span>
            <span className={isCreatingCheckpoint ? 'text-blue-600' : 'text-gray-600'}>
              {isCreatingCheckpoint ? '创建中...' : '空闲'}
            </span>
          </div>
          <div>
            <span className="font-medium">回滚状态：</span>
            <span className={isRollingBack ? 'text-orange-600' : 'text-gray-600'}>
              {isRollingBack ? '回滚中...' : '空闲'}
            </span>
          </div>
        </div>
      </div>

      {/* 创建测试 */}
      <div className="mb-4">
        <h4 className="font-medium mb-2">创建Checkpoint测试</h4>
        <div className="flex gap-2">
          <input
            type="text"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="输入测试消息..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
            disabled={isCreatingCheckpoint}
          />
          <button
            onClick={handleTestCreate}
            disabled={isCreatingCheckpoint || !testMessage.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCreatingCheckpoint ? '创建中...' : '创建'}
          </button>
        </div>
      </div>

      {/* Checkpoint列表 */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h4 className="font-medium">Checkpoint列表</h4>
          <button
            onClick={loadCheckpoints}
            className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            刷新
          </button>
        </div>
        
        {checkpoints.length === 0 ? (
          <p className="text-gray-500 text-sm">暂无checkpoint</p>
        ) : (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {checkpoints.map((checkpoint) => (
              <div
                key={checkpoint.id}
                className={`p-3 border rounded text-sm ${
                  checkpoint.id === currentCheckpointId
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="font-medium">{checkpoint.version}</div>
                    <div className="text-gray-600 text-xs mt-1">
                      {checkpoint.description}
                    </div>
                    <div className="text-gray-500 text-xs mt-1">
                      {new Date(checkpoint.timestamp).toLocaleString('zh-CN')}
                    </div>
                  </div>
                  <div className="flex gap-1 ml-2">
                    {checkpoint.id === currentCheckpointId ? (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
                        当前
                      </span>
                    ) : (
                      <button
                        onClick={() => handleTestRollback(checkpoint.id)}
                        disabled={isRollingBack}
                        className="px-2 py-1 text-xs bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
                      >
                        回滚
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <button
          onClick={loadCheckpoints}
          className="px-4 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
        >
          重新加载
        </button>
        <button
          onClick={handleTestRecover}
          className="px-4 py-2 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
        >
          错误恢复
        </button>
      </div>
    </div>
  );
};

export default CheckpointTestPanel;
