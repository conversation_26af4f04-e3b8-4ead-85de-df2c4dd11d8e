/**
 * Checkpoint工具函数实现
 * 提供checkpoint创建、验证、比较等核心功能
 */

import { 
  Checkpoint, 
  CheckpointStore, 
  CheckpointDiff, 
  CheckpointUtils,
  CHECKPOINT_CONFIG 
} from '../types/CheckpointTypes';

/**
 * Checkpoint工具函数实现类
 */
export class CheckpointUtilsImpl implements CheckpointUtils {
  
  /**
   * 生成唯一的checkpoint ID
   * 格式：checkpoint_${timestamp}_${randomId}
   */
  generateCheckpointId(): string {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    return `checkpoint_${timestamp}_${randomId}`;
  }

  /**
   * 生成版本号
   * 基于现有checkpoint自动递增版本号
   */
  generateVersion(existingCheckpoints: Checkpoint[]): string {
    if (existingCheckpoints.length === 0) {
      return 'v1.0';
    }

    // 获取最新版本号
    const versions = existingCheckpoints
      .map(cp => cp.version)
      .filter(v => CHECKPOINT_CONFIG.VERSION_FORMAT.test(v))
      .sort((a, b) => {
        const [aMajor, aMinor] = a.substring(1).split('.').map(Number);
        const [bMajor, bMinor] = b.substring(1).split('.').map(Number);
        
        if (aMajor !== bMajor) return bMajor - aMajor;
        return bMinor - aMinor;
      });

    if (versions.length === 0) {
      return 'v1.0';
    }

    const latestVersion = versions[0];
    const [major, minor] = latestVersion.substring(1).split('.').map(Number);
    
    // 递增小版本号
    return `v${major}.${minor + 1}`;
  }

  /**
   * 验证checkpoint数据完整性
   */
  validateCheckpoint(checkpoint: Checkpoint): boolean {
    try {
      // 验证基本字段
      if (!checkpoint.id || !checkpoint.version || !checkpoint.timestamp) {
        console.error('[CheckpointUtils] 缺少必要字段');
        return false;
      }

      // 验证ID格式
      if (!CHECKPOINT_CONFIG.ID_FORMAT.test(checkpoint.id)) {
        console.error('[CheckpointUtils] ID格式不正确:', checkpoint.id);
        return false;
      }

      // 验证版本格式
      if (!CHECKPOINT_CONFIG.VERSION_FORMAT.test(checkpoint.version)) {
        console.error('[CheckpointUtils] 版本格式不正确:', checkpoint.version);
        return false;
      }

      // 验证时间戳
      const timestamp = new Date(checkpoint.timestamp);
      if (isNaN(timestamp.getTime())) {
        console.error('[CheckpointUtils] 时间戳格式不正确:', checkpoint.timestamp);
        return false;
      }

      // 验证状态结构
      if (!checkpoint.state || !checkpoint.state.scripts || !checkpoint.state.scene || !checkpoint.state.messages) {
        console.error('[CheckpointUtils] 状态结构不完整');
        return false;
      }

      // 验证元数据
      if (!checkpoint.metadata || !checkpoint.metadata.userAction) {
        console.error('[CheckpointUtils] 元数据不完整');
        return false;
      }

      return true;
    } catch (error) {
      console.error('[CheckpointUtils] 验证checkpoint时发生错误:', error);
      return false;
    }
  }

  /**
   * 比较两个checkpoint的差异
   */
  compareCheckpoints(checkpoint1: Checkpoint, checkpoint2: Checkpoint): CheckpointDiff {
    const diff: CheckpointDiff = {
      scripts: {
        added: [],
        removed: [],
        modified: []
      },
      scene: {
        nodesChanged: false,
        selectedNodeChanged: false,
        statsChanged: false
      },
      messages: {
        added: 0,
        removed: 0
      }
    };

    // 比较脚本差异
    const scripts1 = new Map(checkpoint1.state.scripts.map(s => [s.id, s]));
    const scripts2 = new Map(checkpoint2.state.scripts.map(s => [s.id, s]));

    // 找出新增的脚本
    for (const [id, script] of scripts2) {
      if (!scripts1.has(id)) {
        diff.scripts.added.push(script.name);
      } else {
        // 检查是否修改
        const oldScript = scripts1.get(id)!;
        if (oldScript.scriptContent !== script.scriptContent || 
            oldScript.isActive !== script.isActive) {
          diff.scripts.modified.push(script.name);
        }
      }
    }

    // 找出删除的脚本
    for (const [id, script] of scripts1) {
      if (!scripts2.has(id)) {
        diff.scripts.removed.push(script.name);
      }
    }

    // 比较场景差异
    diff.scene.nodesChanged = checkpoint1.state.scene.nodes.length !== checkpoint2.state.scene.nodes.length ||
      JSON.stringify(checkpoint1.state.scene.nodes) !== JSON.stringify(checkpoint2.state.scene.nodes);
    
    diff.scene.selectedNodeChanged = checkpoint1.state.scene.selectedNode?.id !== checkpoint2.state.scene.selectedNode?.id;
    
    diff.scene.statsChanged = JSON.stringify(checkpoint1.state.scene.nodeStats) !== JSON.stringify(checkpoint2.state.scene.nodeStats);

    // 比较消息差异
    const messageCount1 = checkpoint1.state.messages.length;
    const messageCount2 = checkpoint2.state.messages.length;
    
    if (messageCount2 > messageCount1) {
      diff.messages.added = messageCount2 - messageCount1;
    } else if (messageCount1 > messageCount2) {
      diff.messages.removed = messageCount1 - messageCount2;
    }

    return diff;
  }

  /**
   * 清理过期checkpoint
   * 保留最新的checkpoint，删除超出限制的旧checkpoint
   */
  cleanupOldCheckpoints(checkpoints: Checkpoint[], maxCount: number): Checkpoint[] {
    if (checkpoints.length <= maxCount) {
      return checkpoints;
    }

    // 按时间戳排序，保留最新的
    const sortedCheckpoints = [...checkpoints].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // 保留重要的checkpoint
    const importantCheckpoints = sortedCheckpoints.filter(cp => cp.metadata.isImportant);
    const regularCheckpoints = sortedCheckpoints.filter(cp => !cp.metadata.isImportant);

    // 计算可以保留的普通checkpoint数量
    const availableSlots = Math.max(0, maxCount - importantCheckpoints.length);
    const keptRegularCheckpoints = regularCheckpoints.slice(0, availableSlots);

    // 合并并重新排序
    const result = [...importantCheckpoints, ...keptRegularCheckpoints]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`[CheckpointUtils] 清理checkpoint: ${checkpoints.length} -> ${result.length}`);
    
    return result;
  }

  /**
   * 创建默认的checkpoint存储结构
   */
  createDefaultStore(): CheckpointStore {
    return {
      checkpoints: [],
      currentCheckpointId: null,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
      metadata: {
        totalCount: 0,
        maxCount: CHECKPOINT_CONFIG.DEFAULT_MAX_COUNT,
        autoCleanup: true
      }
    };
  }

  /**
   * 格式化checkpoint显示信息
   */
  formatCheckpointInfo(checkpoint: Checkpoint): string {
    const date = new Date(checkpoint.timestamp);
    const timeStr = date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    return `${checkpoint.version} - ${timeStr}`;
  }

  /**
   * 获取checkpoint摘要信息
   */
  getCheckpointSummary(checkpoint: Checkpoint): string {
    const { metadata } = checkpoint;
    const parts: string[] = [];

    if (metadata.generatedScripts.length > 0) {
      parts.push(`生成${metadata.generatedScripts.length}个脚本`);
    }

    if (metadata.appliedChanges.length > 0) {
      parts.push(`应用${metadata.appliedChanges.length}项变更`);
    }

    return parts.length > 0 ? parts.join('，') : '无变更';
  }
}

// 导出单例实例
export const checkpointUtils = new CheckpointUtilsImpl();
