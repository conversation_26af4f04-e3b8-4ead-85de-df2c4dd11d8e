/**
 * ModelManager - 模型管理组件
 * 提供模型列表显示、预览、设置默认等功能
 */

'use client';

import React, { useCallback } from 'react';
import { ModelInfo } from '../../../../src/types/NodeTypes';

interface ModelManagerProps {
  nodeId: string;
  models: ModelInfo[];
  currentModelId?: string;
  onPreviewModel: (nodeId: string, model: ModelInfo) => void;
  onSetDefaultModel: (nodeId: string, model: ModelInfo) => void;
  onRemoveModel: (nodeId: string, modelId: string) => void;
  isUploadingModel: boolean;
  uploadError: string | null;
}

export const ModelManager: React.FC<ModelManagerProps> = ({
  nodeId,
  models,
  currentModelId,
  onPreviewModel,
  onSetDefaultModel,
  onRemoveModel,
  isUploadingModel,
  uploadError
}) => {
  // 格式化文件大小显示
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  }, []);



  if (models.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">
        <div className="w-12 h-12 mx-auto mb-3 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <p className="text-sm">暂无模型文件</p>
        <p className="text-xs mt-1">上传FBX/GLB/GLTF模型文件开始使用</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* 错误提示 */}
      {uploadError && (
        <div className="p-3 bg-red-100 border border-red-300 rounded-lg text-sm text-red-800">
          {uploadError}
        </div>
      )}

      {/* 模型列表 */}
      <div className="space-y-2">
        {models.map((model) => {
          const isCurrent = currentModelId === model.id;
          const isDefault = model.isDefault;

          return (
            <div
              key={model.id}
              className={`p-3 rounded-lg border transition-colors ${
                isDefault
                  ? 'border-blue-500 bg-blue-50'
                  : isCurrent
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {/* 模型信息头部 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {model.name}
                    </h4>
                    {isDefault && (
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        默认
                      </span>
                    )}
                    {isCurrent && !isDefault && (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                        当前
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>格式: {model.fileType.toUpperCase()}</span>
                    <span>大小: {formatFileSize(model.fileSize)}</span>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-3">
                  {/* 预览按钮 */}
                  <button
                    onClick={() => onPreviewModel(nodeId, model)}
                    className="p-1.5 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                    title="预览模型"
                    disabled={isUploadingModel}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>

                  {/* 设为默认按钮 */}
                  {!isDefault && (
                    <button
                      onClick={() => onSetDefaultModel(nodeId, model)}
                      className="p-1.5 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                      title="设为默认模型"
                      disabled={isUploadingModel}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                  )}

                  {/* 删除按钮 */}
                  <button
                    onClick={() => onRemoveModel(nodeId, model.id)}
                    className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                    title="删除模型"
                    disabled={isUploadingModel}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* 模型详细信息 */}
              {model.hasAnimations && (
                <div className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">包含动画</span>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ModelManager;
