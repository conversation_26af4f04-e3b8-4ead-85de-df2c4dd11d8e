/**
 * 代码质量检查器
 * 专门检查Three.js和React代码的质量问题
 */

export interface QualityIssue {
  type: 'error' | 'warning' | 'info';
  category: 'typescript' | 'threejs' | 'react' | 'performance' | 'security';
  message: string;
  line?: number;
  column?: number;
  suggestion?: string;
}

export interface QualityReport {
  score: number; // 0-100
  issues: QualityIssue[];
  summary: {
    errors: number;
    warnings: number;
    infos: number;
  };
  recommendations: string[];
}

/**
 * 代码质量检查器
 */
export class CodeQualityChecker {
  private threeJSPatterns: RegExp[];
  private reactPatterns: RegExp[];
  private performancePatterns: RegExp[];

  constructor() {
    this.initializePatterns();
  }

  /**
   * 初始化检查模式
   */
  private initializePatterns(): void {
    // Three.js相关模式
    this.threeJSPatterns = [
      /new\s+THREE\.(\w+)\(/g,
      /\.dispose\(\)/g,
      /requestAnimationFrame/g,
      /renderer\.render/g,
      /scene\.add/g,
      /camera\.updateProjectionMatrix/g
    ];

    // React相关模式
    this.reactPatterns = [
      /useEffect/g,
      /useState/g,
      /useRef/g,
      /useCallback/g,
      /useMemo/g
    ];

    // 性能相关模式
    this.performancePatterns = [
      /console\.log/g,
      /console\.warn/g,
      /console\.error/g,
      /debugger/g
    ];
  }

  /**
   * 检查代码质量
   */
  checkQuality(code: string): QualityReport {
    const issues: QualityIssue[] = [];
    
    // 检查TypeScript类型
    issues.push(...this.checkTypeScript(code));
    
    // 检查Three.js使用
    issues.push(...this.checkThreeJS(code));
    
    // 检查React使用
    issues.push(...this.checkReact(code));
    
    // 检查性能问题
    issues.push(...this.checkPerformance(code));
    
    // 检查安全问题
    issues.push(...this.checkSecurity(code));

    const summary = this.calculateSummary(issues);
    const score = this.calculateScore(issues);
    const recommendations = this.generateRecommendations(issues);

    return {
      score,
      issues,
      summary,
      recommendations
    };
  }

  /**
   * 检查TypeScript类型问题
   */
  private checkTypeScript(code: string): QualityIssue[] {
    const issues: QualityIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      // 检查any类型使用
      if (line.includes(': any') || line.includes('<any>')) {
        issues.push({
          type: 'warning',
          category: 'typescript',
          message: 'Avoid using "any" type, use specific types instead',
          line: index + 1,
          suggestion: 'Define proper TypeScript interfaces or use union types'
        });
      }

      // 检查未定义的变量
      if (line.includes('undefined') && !line.includes('!== undefined') && !line.includes('=== undefined')) {
        issues.push({
          type: 'warning',
          category: 'typescript',
          message: 'Potential undefined variable usage',
          line: index + 1,
          suggestion: 'Add proper null/undefined checks'
        });
      }

      // 检查缺少类型注解
      if (line.includes('function ') && !line.includes(': ') && !line.includes('=>')) {
        issues.push({
          type: 'info',
          category: 'typescript',
          message: 'Function missing return type annotation',
          line: index + 1,
          suggestion: 'Add explicit return type annotation'
        });
      }
    });

    return issues;
  }

  /**
   * 检查Three.js使用问题
   */
  private checkThreeJS(code: string): QualityIssue[] {
    const issues: QualityIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      // 检查是否缺少dispose调用
      if (line.includes('new THREE.') && (line.includes('Geometry') || line.includes('Material') || line.includes('Texture'))) {
        const hasDispose = code.includes('.dispose()');
        if (!hasDispose) {
          issues.push({
            type: 'warning',
            category: 'threejs',
            message: 'Three.js objects should be disposed to prevent memory leaks',
            line: index + 1,
            suggestion: 'Add .dispose() calls in cleanup function'
          });
        }
      }

      // 检查渲染循环
      if (line.includes('renderer.render') && !code.includes('requestAnimationFrame')) {
        issues.push({
          type: 'error',
          category: 'threejs',
          message: 'Missing animation loop with requestAnimationFrame',
          line: index + 1,
          suggestion: 'Use requestAnimationFrame for smooth animation'
        });
      }

      // 检查相机更新
      if (line.includes('camera.aspect') && !line.includes('updateProjectionMatrix')) {
        issues.push({
          type: 'error',
          category: 'threejs',
          message: 'Camera aspect change requires updateProjectionMatrix call',
          line: index + 1,
          suggestion: 'Call camera.updateProjectionMatrix() after changing aspect ratio'
        });
      }

      // 检查控制器更新
      if (line.includes('OrbitControls') && !code.includes('controls.update()')) {
        issues.push({
          type: 'warning',
          category: 'threejs',
          message: 'OrbitControls should be updated in animation loop',
          line: index + 1,
          suggestion: 'Call controls.update() in your animation loop'
        });
      }
    });

    return issues;
  }

  /**
   * 检查React使用问题
   */
  private checkReact(code: string): QualityIssue[] {
    const issues: QualityIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      // 检查useEffect依赖
      if (line.includes('useEffect') && line.includes('[]')) {
        const nextLines = lines.slice(index, index + 10).join('\n');
        if (nextLines.includes('state') || nextLines.includes('props')) {
          issues.push({
            type: 'warning',
            category: 'react',
            message: 'useEffect may be missing dependencies',
            line: index + 1,
            suggestion: 'Add all used variables to dependency array'
          });
        }
      }

      // 检查内存泄漏
      if (line.includes('useEffect') && !code.includes('return () =>')) {
        issues.push({
          type: 'warning',
          category: 'react',
          message: 'useEffect should include cleanup function for Three.js objects',
          line: index + 1,
          suggestion: 'Add cleanup function to dispose Three.js resources'
        });
      }

      // 检查状态更新
      if (line.includes('setState') && line.includes('state.')) {
        issues.push({
          type: 'warning',
          category: 'react',
          message: 'Direct state mutation detected',
          line: index + 1,
          suggestion: 'Use functional updates or spread operator'
        });
      }
    });

    return issues;
  }

  /**
   * 检查性能问题
   */
  private checkPerformance(code: string): QualityIssue[] {
    const issues: QualityIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      // 检查console语句
      if (line.includes('console.log') || line.includes('console.warn')) {
        issues.push({
          type: 'info',
          category: 'performance',
          message: 'Console statements should be removed in production',
          line: index + 1,
          suggestion: 'Remove or replace with proper logging'
        });
      }

      // 检查在渲染循环中创建对象
      if (line.includes('new THREE.') && (code.includes('requestAnimationFrame') || code.includes('animate'))) {
        const animationContext = this.isInAnimationLoop(lines, index);
        if (animationContext) {
          issues.push({
            type: 'error',
            category: 'performance',
            message: 'Creating Three.js objects in animation loop causes performance issues',
            line: index + 1,
            suggestion: 'Move object creation outside the animation loop'
          });
        }
      }

      // 检查大量DOM操作
      if (line.includes('document.createElement') || line.includes('appendChild')) {
        issues.push({
          type: 'warning',
          category: 'performance',
          message: 'Direct DOM manipulation in React component',
          line: index + 1,
          suggestion: 'Use React refs or state management instead'
        });
      }
    });

    return issues;
  }

  /**
   * 检查安全问题
   */
  private checkSecurity(code: string): QualityIssue[] {
    const issues: QualityIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      // 检查eval使用
      if (line.includes('eval(')) {
        issues.push({
          type: 'error',
          category: 'security',
          message: 'eval() usage is dangerous and should be avoided',
          line: index + 1,
          suggestion: 'Use safer alternatives like JSON.parse() or proper parsing'
        });
      }

      // 检查innerHTML使用
      if (line.includes('innerHTML')) {
        issues.push({
          type: 'warning',
          category: 'security',
          message: 'innerHTML usage can lead to XSS vulnerabilities',
          line: index + 1,
          suggestion: 'Use textContent or React JSX instead'
        });
      }
    });

    return issues;
  }

  /**
   * 检查是否在动画循环中
   */
  private isInAnimationLoop(lines: string[], currentIndex: number): boolean {
    // 向上查找函数定义
    for (let i = currentIndex; i >= 0; i--) {
      const line = lines[i];
      if (line.includes('function animate') || line.includes('const animate') || line.includes('animate =')) {
        return true;
      }
      if (line.includes('function ') && !line.includes('animate')) {
        break;
      }
    }
    return false;
  }

  /**
   * 计算问题摘要
   */
  private calculateSummary(issues: QualityIssue[]): { errors: number; warnings: number; infos: number } {
    return {
      errors: issues.filter(issue => issue.type === 'error').length,
      warnings: issues.filter(issue => issue.type === 'warning').length,
      infos: issues.filter(issue => issue.type === 'info').length
    };
  }

  /**
   * 计算质量分数
   */
  private calculateScore(issues: QualityIssue[]): number {
    const summary = this.calculateSummary(issues);
    const totalIssues = summary.errors + summary.warnings + summary.infos;
    
    if (totalIssues === 0) return 100;
    
    // 错误权重更高
    const weightedScore = 100 - (summary.errors * 10 + summary.warnings * 5 + summary.infos * 1);
    return Math.max(0, Math.min(100, weightedScore));
  }

  /**
   * 生成改进建议
   */
  private generateRecommendations(issues: QualityIssue[]): string[] {
    const recommendations: string[] = [];
    const categories = new Set(issues.map(issue => issue.category));

    if (categories.has('typescript')) {
      recommendations.push('Improve TypeScript usage by adding proper type annotations and avoiding "any" type');
    }

    if (categories.has('threejs')) {
      recommendations.push('Follow Three.js best practices: dispose resources, use proper animation loops, and update camera matrices');
    }

    if (categories.has('react')) {
      recommendations.push('Optimize React usage: add proper useEffect dependencies and cleanup functions');
    }

    if (categories.has('performance')) {
      recommendations.push('Improve performance: remove console statements and avoid object creation in animation loops');
    }

    if (categories.has('security')) {
      recommendations.push('Address security concerns: avoid eval() and innerHTML usage');
    }

    return recommendations;
  }
}
