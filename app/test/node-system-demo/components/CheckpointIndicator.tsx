/**
 * CheckpointIndicator - Checkpoint版本标识组件
 * 在用户消息下方显示checkpoint版本标识，提供回滚功能
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Checkpoint } from '../../../../src/types/CheckpointTypes';
import { checkpointUtils } from '../../../../src/utils/CheckpointUtils';

interface CheckpointIndicatorProps {
  checkpoint: Checkpoint;
  isCurrentCheckpoint: boolean;
  onRollback: (checkpointId: string) => Promise<boolean>;
  isRollingBack: boolean;
  className?: string;
}

export const CheckpointIndicator: React.FC<CheckpointIndicatorProps> = ({
  checkpoint,
  isCurrentCheckpoint,
  onRollback,
  isRollingBack,
  className = ''
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 处理回滚确认
  const handleRollbackConfirm = useCallback(async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const success = await onRollback(checkpoint.id);
      if (success) {
        setShowConfirmDialog(false);
        setError(null);
      } else {
        setError('回滚操作失败，请稍后重试');
      }
    } catch (error) {
      console.error('[CheckpointIndicator] 回滚失败:', error);
      setError(error instanceof Error ? error.message : '回滚操作失败');
    } finally {
      setIsProcessing(false);
    }
  }, [checkpoint.id, onRollback]);

  // 处理点击回滚按钮
  const handleRollbackClick = useCallback(() => {
    // 允许回滚到任何checkpoint，包括当前checkpoint
    // 这样用户可以回滚到发消息但脚本还没生成的状态
    setShowConfirmDialog(true);
  }, []);

  // 格式化时间显示
  const formatTime = useCallback((timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  }, []);

  // 获取checkpoint摘要
  const summary = checkpointUtils.getCheckpointSummary(checkpoint);

  return (
    <>
      <div className={`flex items-center gap-2 mt-2 ${className}`}>
        {/* Checkpoint标识 - 仿照Augment样式 */}
        <div className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-all cursor-pointer ${
          isCurrentCheckpoint
            ? 'bg-gray-800 text-white shadow-sm hover:bg-gray-700'
            : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
        }`}
        onClick={handleRollbackClick}
        title={isCurrentCheckpoint ? "点击回滚到此版本（清除后续变更）" : "点击回滚到此版本"}
        >
          {/* 保存图标 */}
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>

          <span>Checkpoint {checkpoint.version.replace('v', '')}</span>
          {isCurrentCheckpoint && <span className="text-xs opacity-70">(当前)</span>}

          {/* 回滚图标 - 所有版本都显示 */}
          <svg
            className={`w-4 h-4 ${isCurrentCheckpoint ? 'opacity-50' : 'opacity-70'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
            />
          </svg>
        </div>

        {/* 处理状态指示器 */}
        {isProcessing && (
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
            <span>回滚中...</span>
          </div>
        )}
      </div>

      {/* 摘要信息 - 更简洁的样式 */}
      {summary && (
        <div className="mt-1 text-xs text-gray-400 pl-3">
          {summary}
        </div>
      )}

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              确认回滚操作
            </h3>
            
            <div className="mb-4 text-sm text-gray-600">
              <p className="mb-2">
                您即将回滚到版本 <span className="font-mono font-medium">{checkpoint.version}</span>
              </p>
              <p className="mb-2">
                <strong>创建时间：</strong>{formatTime(checkpoint.timestamp)}
              </p>
              <p className="mb-2">
                <strong>描述：</strong>{checkpoint.description}
              </p>
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 text-sm">
                  ⚠️ <strong>警告：</strong>此操作将{isCurrentCheckpoint ? '清除当前版本生成的所有内容' : '清除该版本之后的所有变更'}，包括：
                </p>
                <ul className="mt-2 text-yellow-700 text-sm list-disc list-inside">
                  {isCurrentCheckpoint ? (
                    <>
                      <li>当前生成的脚本和功能</li>
                      <li>脚本应用后的场景变更</li>
                      <li>Agent的回复消息</li>
                    </>
                  ) : (
                    <>
                      <li>后续的对话消息</li>
                      <li>生成的脚本和功能</li>
                      <li>场景状态变更</li>
                    </>
                  )}
                </ul>
                <p className="mt-2 text-yellow-800 text-sm">
                  此操作无法撤销，请谨慎操作。
                </p>
              </div>
            </div>

            {/* 错误信息显示 */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-red-800 text-sm">
                  ❌ <strong>错误：</strong>{error}
                </p>
              </div>
            )}

            <div className="flex gap-3 justify-end">
              <button
                onClick={() => {
                  setShowConfirmDialog(false);
                  setError(null);
                }}
                disabled={isProcessing}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors disabled:opacity-50"
              >
                取消
              </button>
              <button
                onClick={handleRollbackConfirm}
                disabled={isProcessing}
                className="px-4 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? '回滚中...' : '确认回滚'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CheckpointIndicator;
