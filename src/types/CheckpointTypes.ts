/**
 * Checkpoint版本管理系统类型定义
 * 为PlayableGen平台提供用户友好的版本管理功能
 */

import { GameNodeProperties } from './NodeTypes';

// 从其他模块导入的类型
export interface AgentMessage {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface PersistentFeature {
  id: string;
  name: string;
  description: string;
  scriptContent: string;
  addedAt: string;
  isActive: boolean;
}

/**
 * Checkpoint核心数据结构
 * 保存某个时间点的完整系统状态
 */
export interface Checkpoint {
  /** 唯一标识符，格式：checkpoint_${timestamp}_${randomId} */
  id: string;
  
  /** 版本号，格式：v1.0, v1.1, v2.0 等 */
  version: string;
  
  /** 关联的用户消息ID */
  messageId: string;
  
  /** 创建时间戳（ISO格式） */
  timestamp: string;
  
  /** 用户描述/请求内容 */
  description: string;
  
  /** 系统状态快照 */
  state: CheckpointState;
  
  /** 元数据信息 */
  metadata: CheckpointMetadata;
}

/**
 * Checkpoint状态快照
 * 包含需要恢复的所有系统状态
 */
export interface CheckpointState {
  /** 脚本状态 - 所有持久化功能 */
  scripts: PersistentFeature[];
  
  /** 场景状态 */
  scene: {
    /** 所有节点信息 */
    nodes: GameNodeProperties[];
    /** 当前选中的节点 */
    selectedNode: GameNodeProperties | null;
    /** 节点统计信息 */
    nodeStats: { total: number; byType: Record<string, number> };
  };
  
  /** 聊天状态 - 到该checkpoint为止的所有消息 */
  messages: AgentMessage[];
  
  /** 会话信息 */
  session: {
    sessionId: string;
    messageCount: number;
  };
}

/**
 * Checkpoint元数据
 * 记录变更信息和操作历史
 */
export interface CheckpointMetadata {
  /** 用户的原始请求 */
  userAction: string;
  
  /** 本次生成的脚本ID列表 */
  generatedScripts: string[];
  
  /** 应用的变更描述列表 */
  appliedChanges: string[];
  
  /** 创建类型：auto（自动）或 manual（手动） */
  creationType: 'auto' | 'manual';
  
  /** 是否为重要节点（用户可标记） */
  isImportant: boolean;
  
  /** 用户自定义标签 */
  tags: string[];
}

/**
 * Checkpoint存储结构
 * 管理所有checkpoint的存储和索引
 */
export interface CheckpointStore {
  /** 所有checkpoint列表，按时间顺序排列 */
  checkpoints: Checkpoint[];
  
  /** 当前活跃的checkpoint ID */
  currentCheckpointId: string | null;
  
  /** 最后更新时间 */
  lastUpdated: string;
  
  /** 存储版本，用于数据迁移 */
  version: string;
  
  /** 存储元数据 */
  metadata: {
    /** 总checkpoint数量 */
    totalCount: number;
    /** 最大保存数量（超出时自动清理旧的） */
    maxCount: number;
    /** 自动清理策略 */
    autoCleanup: boolean;
  };
}

/**
 * Checkpoint操作结果
 */
export interface CheckpointOperationResult {
  success: boolean;
  message: string;
  data?: {
    checkpoint?: Checkpoint;
    checkpoints?: Checkpoint[];
    restoredState?: CheckpointState;
  };
  error?: string;
}

/**
 * Checkpoint创建选项
 */
export interface CreateCheckpointOptions {
  /** 用户消息ID */
  messageId: string;
  /** 描述 */
  description: string;
  /** 用户操作 */
  userAction: string;
  /** 生成的脚本ID列表 */
  generatedScripts?: string[];
  /** 是否为重要节点 */
  isImportant?: boolean;
  /** 自定义标签 */
  tags?: string[];
}

/**
 * Checkpoint回滚选项
 */
export interface RollbackCheckpointOptions {
  /** 目标checkpoint ID */
  checkpointId: string;
  /** 是否清除后续checkpoint */
  clearSubsequent?: boolean;
  /** 是否强制回滚（忽略冲突） */
  force?: boolean;
}

/**
 * Checkpoint工具函数类型
 */
export interface CheckpointUtils {
  /** 生成checkpoint ID */
  generateCheckpointId(): string;
  
  /** 生成版本号 */
  generateVersion(existingCheckpoints: Checkpoint[]): string;
  
  /** 验证checkpoint数据完整性 */
  validateCheckpoint(checkpoint: Checkpoint): boolean;
  
  /** 比较两个checkpoint的差异 */
  compareCheckpoints(checkpoint1: Checkpoint, checkpoint2: Checkpoint): CheckpointDiff;
  
  /** 清理过期checkpoint */
  cleanupOldCheckpoints(checkpoints: Checkpoint[], maxCount: number): Checkpoint[];
}

/**
 * Checkpoint差异比较结果
 */
export interface CheckpointDiff {
  /** 脚本变更 */
  scripts: {
    added: string[];
    removed: string[];
    modified: string[];
  };
  
  /** 场景变更 */
  scene: {
    nodesChanged: boolean;
    selectedNodeChanged: boolean;
    statsChanged: boolean;
  };
  
  /** 消息变更 */
  messages: {
    added: number;
    removed: number;
  };
}

/**
 * 默认配置常量
 */
export const CHECKPOINT_CONFIG = {
  /** 默认最大保存数量 */
  DEFAULT_MAX_COUNT: 50,
  
  /** 版本号格式 */
  VERSION_FORMAT: /^v\d+\.\d+$/,
  
  /** ID格式 */
  ID_FORMAT: /^checkpoint_\d+_[a-z0-9]+$/,
  
  /** 存储文件路径 */
  STORAGE_FILE: 'data/checkpoints.json',
  
  /** 自动清理阈值 */
  AUTO_CLEANUP_THRESHOLD: 0.8, // 达到80%时开始清理
} as const;
