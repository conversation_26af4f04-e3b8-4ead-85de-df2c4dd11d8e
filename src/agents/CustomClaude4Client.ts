import { BaseMessage, AIMessage, HumanMessage, SystemMessage } from "@langchain/core/messages";
import { tokenTracker } from "../utils/TokenUsageTracker";
import { AGENT_CONFIG } from "../config/agent-config";

// Claude4客户端配置
export interface Claude4Config {
  apiKey: string;
  baseURL: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  // Token统计相关
  agentType?: string;
  agentName?: string;
  threadId?: string;
}

// 消息接口
interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// API请求体接口
interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  max_tokens?: number;
  temperature?: number;
}

// API响应接口
interface ChatCompletionResponse {
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// 流式响应接口
interface StreamChunk {
  choices: Array<{
    delta: {
      role?: string;
      content?: string;
    };
    finish_reason?: string;
  }>;
}

// 流式回调接口
export interface StreamCallback {
  onToken?: (token: string) => void;
  onProgress?: (progress: { current: number; total: number }) => void;
  onStatus?: (status: string) => void;
}

// 自定义Claude4客户端
export class CustomClaude4Client {
  private config: Claude4Config;
  private streamCallback?: StreamCallback;

  constructor(config: Claude4Config) {
    this.config = {
      model: AGENT_CONFIG.API.MODEL,
      maxTokens: AGENT_CONFIG.MAX_TOKENS.DEFAULT,
      temperature: AGENT_CONFIG.TEMPERATURE.DEFAULT,
      timeout: AGENT_CONFIG.TIMEOUT.DEFAULT,
      ...config
    };
  }

  // 设置流式回调
  setStreamCallback(callback: StreamCallback): void {
    this.streamCallback = callback;
  }

  // 调用Claude4模型（默认使用流式输出）
  async invoke(messages: BaseMessage[] | ChatMessage[], taskDescription?: string): Promise<AIMessage> {
    // 如果没有设置流式回调，创建一个默认的
    if (!this.streamCallback) {
      this.streamCallback = {
        onToken: (token) => {
          // 默认不输出token，避免控制台噪音
        },
        onStatus: (status) => {
          console.log(`[Claude4] ${status}`);
        }
      };
    }

    // 统一使用流式调用
    try {
      return await this.invokeStream(messages, this.streamCallback, taskDescription);
    } catch (error) {
      console.warn('[Claude4] 流式调用失败，回退到普通调用:', error);
      this.streamCallback.onStatus?.('流式调用失败，切换到普通模式...');
      // 继续使用普通调用
    }

    // 否则使用普通调用
    const startTime = Date.now();

    try {
      // 转换消息格式
      const chatMessages = this.convertMessages(messages);

      console.log(`[Claude4] 发送请求到 ${this.config.baseURL}`);
      console.log(`[Claude4] 模型: ${this.config.model}`);
      console.log(`[Claude4] 消息数量: ${chatMessages.length}`);

      // 构建请求体
      const requestBody: ChatCompletionRequest = {
        model: this.config.model!,
        messages: chatMessages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      };

      // 发送HTTP请求
      const response = await this.makeRequest('/chat/completions', requestBody);
      const duration = Date.now() - startTime;

      const content = response.choices[0]?.message?.content || '';

      console.log(`[Claude4] 响应长度: ${content.length} 字符`);
      console.log(`[Claude4] 请求耗时: ${duration}ms`);

      // 记录token使用情况
      if (response.usage && this.config.agentType) {
        tokenTracker.recordUsage({
          agentType: this.config.agentType,
          agentName: this.config.agentName || this.config.agentType,
          threadId: this.config.threadId || 'default',
          model: this.config.model!,
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
          taskDescription,
          duration
        });
      }

      return new AIMessage({
        content,
        response_metadata: {
          model: this.config.model,
          usage: response.usage,
          finish_reason: response.choices[0]?.finish_reason
        }
      });
    } catch (error) {
      console.error('[Claude4] 请求失败:', error);
      throw new Error(`Claude4 API调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 流式调用Claude4模型
  async invokeStream(
    messages: BaseMessage[] | ChatMessage[],
    callback: StreamCallback,
    taskDescription?: string
  ): Promise<AIMessage> {
    const startTime = Date.now();
    let fullContent = '';

    try {
      // 转换消息格式
      const chatMessages = this.convertMessages(messages);

      callback.onStatus?.('正在连接Claude API...');

      // 构建请求体
      const requestBody = {
        model: this.config.model!,
        messages: chatMessages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true // 启用流式响应
      };

      // 发送流式请求
      const url = `${this.config.baseURL.replace(/\/$/, '')}/chat/completions`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[Claude4] 流式请求失败详情:`, {
          status: response.status,
          statusText: response.statusText,
          url: url,
          headers: Object.fromEntries(response.headers.entries()),
          body: errorText
        });
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      callback.onStatus?.('开始接收响应...');

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              break;
            }

            try {
              const chunk: StreamChunk = JSON.parse(data);
              const content = chunk.choices[0]?.delta?.content;

              if (content) {
                fullContent += content;
                callback.onToken?.(content);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }

      const duration = Date.now() - startTime;
      callback.onStatus?.('响应完成');

      // 记录token使用情况（流式响应通常没有usage信息，使用估算）
      if (this.config.agentType) {
        const estimatedTokens = Math.ceil(fullContent.length / 4); // 粗略估算
        tokenTracker.recordUsage({
          agentType: this.config.agentType,
          agentName: this.config.agentName || this.config.agentType,
          threadId: this.config.threadId || 'default',
          model: this.config.model!,
          promptTokens: 0, // 流式响应通常不返回详细token信息
          completionTokens: estimatedTokens,
          totalTokens: estimatedTokens,
          taskDescription,
          duration
        });
      }

      return new AIMessage({
        content: fullContent,
        response_metadata: {
          model: this.config.model,
          finish_reason: 'stop'
        }
      });
    } catch (error) {
      console.error('[Claude4] 流式请求失败:', error);
      callback.onStatus?.(`请求失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  // HTTP请求方法
  private async makeRequest(endpoint: string, body: any): Promise<ChatCompletionResponse> {
    const url = `${this.config.baseURL.replace(/\/$/, '')}${endpoint}`;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`请求超时 (${this.config.timeout}ms)`);
      }
      throw error;
    }
  }

  // 转换消息格式
  private convertMessages(messages: BaseMessage[] | ChatMessage[]): ChatMessage[] {
    return messages.map(msg => {
      if ('role' in msg && 'content' in msg) {
        // 已经是ChatMessage格式
        return msg as ChatMessage;
      }

      // LangChain BaseMessage格式
      const baseMsg = msg as BaseMessage;
      let role: 'system' | 'user' | 'assistant';

      if (baseMsg instanceof SystemMessage) {
        role = 'system';
      } else if (baseMsg instanceof HumanMessage) {
        role = 'user';
      } else if (baseMsg instanceof AIMessage) {
        role = 'assistant';
      } else {
        // 默认处理
        role = 'user';
      }

      return {
        role,
        content: typeof baseMsg.content === 'string' ? baseMsg.content : JSON.stringify(baseMsg.content)
      };
    });
  }

  // 测试连接
  async testConnection(): Promise<{ success: boolean; message: string; model?: string; response?: string }> {
    try {
      const testMessage: ChatMessage[] = [{
        role: 'user',
        content: '你是什么模型？请简短回答。'
      }];

      const response = await this.invoke(testMessage.map(msg => new HumanMessage(msg.content)));

      return {
        success: true,
        message: '连接成功',
        model: this.config.model,
        response: response.content as string
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '连接测试失败'
      };
    }
  }

  // 获取配置信息
  getConfig(): Omit<Claude4Config, 'apiKey'> {
    const { apiKey, ...safeConfig } = this.config;
    return safeConfig;
  }

  // 更新配置
  updateConfig(newConfig: Partial<Claude4Config>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[Claude4] 配置已更新');
  }
}

// 基于最佳实践的温度配置 - 使用环境变量
export const AGENT_TEMPERATURE_CONFIGS = {
  // 游戏设计Agent - 需要创意但要保持逻辑性
  GAME_DESIGN: { temperature: AGENT_CONFIG.TEMPERATURE.CREATIVE, top_p: 0.7 },
  // 代码生成Agent - 需要准确性和确定性（参考最佳实践表格）
  CODE_GENERATION: { temperature: AGENT_CONFIG.TEMPERATURE.PRECISE, top_p: 0.1 },
  // 代码审查Agent - 需要客观分析
  CODE_REVIEW: { temperature: AGENT_CONFIG.TEMPERATURE.BALANCED, top_p: 0.2 },
  // 通用Agent - 平衡设置
  DEFAULT: { temperature: AGENT_CONFIG.TEMPERATURE.DEFAULT, top_p: 0.5 }
};

// 默认配置（基于环境变量）
export const DEFAULT_CLAUDE4_CONFIG: Claude4Config = {
  apiKey: AGENT_CONFIG.API.KEY,
  baseURL: AGENT_CONFIG.API.BASE_URL,
  model: AGENT_CONFIG.API.MODEL,
  maxTokens: AGENT_CONFIG.MAX_TOKENS.DEFAULT,
  temperature: AGENT_CONFIG.TEMPERATURE.DEFAULT,
  timeout: AGENT_CONFIG.TIMEOUT.DEFAULT
};

// 工厂函数
export function createClaude4Client(config?: Partial<Claude4Config>): CustomClaude4Client {
  return new CustomClaude4Client({
    ...DEFAULT_CLAUDE4_CONFIG,
    ...config
  });
}
