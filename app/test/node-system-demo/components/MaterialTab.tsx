/**
 * MaterialTab - 材质管理标签页组件
 * 提供材质文件的上传和管理功能
 */

'use client';

import React, { useState, useCallback } from 'react';
import { GameNodeType, GameNodeProperties, MeshNodeProperties, MaterialInfo } from '../../../../src/types/NodeTypes';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import MaterialManager from './MaterialManager';

interface MaterialTabProps {
  selectedNode: GameNodeProperties;
  isDragOverMaterial: boolean;
  isUploadingMaterial: boolean;
  materialError: string | null;
  onSetIsDragOverMaterial: (dragOver: boolean) => void;
  onMaterialUpload: (file: File, nodeId: string) => void;
  onPreviewMaterial: (nodeId: string, material: MaterialInfo) => void;
  onSetDefaultMaterial: (nodeId: string, material: MaterialInfo) => void;
  onRemoveMaterial: (nodeId: string, materialId: string) => void;
}

export const MaterialTab: React.FC<MaterialTabProps> = ({
  selectedNode,
  isDragOverMaterial,
  isUploadingMaterial,
  materialError,
  onSetIsDragOverMaterial,
  onMaterialUpload,
  onPreviewMaterial,
  onSetDefaultMaterial,
  onRemoveMaterial
}) => {
  const { getNodeMaterials, currentMaterial } = useNodeSystem();

  if (selectedNode.type !== GameNodeType.MESH) {
    return (
      <div className="text-center py-6 text-gray-500">
        <p className="text-sm">此节点类型不支持材质</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 材质上传区域 */}
      <div>
        <h4 className="text-sm font-medium mb-3 text-gray-900 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
          </svg>
          材质文件
        </h4>
        <div
          className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
            isDragOverMaterial
              ? 'border-purple-500 bg-purple-50'
              : 'border-gray-300 hover:border-gray-200'
          }`}
          onDragOver={(e) => {
            e.preventDefault();
            onSetIsDragOverMaterial(true);
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            onSetIsDragOverMaterial(false);
          }}
          onDrop={(e) => {
            e.preventDefault();
            onSetIsDragOverMaterial(false);
            const files = Array.from(e.dataTransfer.files);
            const file = files[0];
            if (file) {
              onMaterialUpload(file, selectedNode.id);
            }
          }}
        >
          <input
            type="file"
            accept=".jpg,.jpeg,.png,.bmp,.tga,.json"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                onMaterialUpload(file, selectedNode.id);
              }
            }}
            className="hidden"
            id="material-upload"
            disabled={isUploadingMaterial}
          />
          <label
            htmlFor="material-upload"
            className={`flex flex-col items-center justify-center cursor-pointer ${
              isUploadingMaterial ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <div className="w-8 h-8 mb-2 text-purple-600">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
              </svg>
            </div>
            {isUploadingMaterial ? (
              <div className="flex flex-col items-center">
                <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                <p className="text-sm text-gray-600">正在处理材质...</p>
              </div>
            ) : (
              <>
                <p className="text-sm text-gray-700 mb-1">
                  {isDragOverMaterial ? '松开以上传材质' : '点击或拖拽上传材质文件'}
                </p>
                <p className="text-xs text-gray-600">支持图片和 .json 材质定义</p>
              </>
            )}
          </label>
        </div>

        {materialError && (
          <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-800">
            {materialError}
          </div>
        )}
      </div>

      {/* 材质管理器 */}
      <div>
        <h4 className="text-sm font-medium mb-3 text-gray-900 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          材质管理
        </h4>

        {/* 材质管理器 */}
        <MaterialManager
          nodeId={selectedNode.id}
          materials={getNodeMaterials(selectedNode.id)}
          currentMaterialId={currentMaterial.get(selectedNode.id)}
          onPreviewMaterial={onPreviewMaterial}
          onSetDefaultMaterial={onSetDefaultMaterial}
          onRemoveMaterial={onRemoveMaterial}
          isUploadingMaterial={isUploadingMaterial}
          materialError={materialError}
        />
      </div>
    </div>
  );
};

export default MaterialTab;
