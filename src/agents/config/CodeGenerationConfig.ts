/**
 * 代码生成配置管理
 * 统一管理所有代码生成相关的配置
 */

// 代码生成配置接口
export interface CodeGenerationConfig {
  framework: "three.js";
  outputFormat: "tsx" | "module";
  includeAssets: boolean;
  minify: boolean;
  enableOptimization: boolean;
  enableTypeChecking: boolean;
  maxTokens: number;
  temperature: number;
}

// 生成结果接口
export interface GenerationResult {
  success: boolean;
  code?: string;
  error?: string;
  warnings?: string[];
  metadata?: {
    tokensUsed: number;
    generationTime: number;
    codeLines: number;
    dependencies: string[];
  };
}

// 优化配置接口
export interface OptimizationConfig {
  enableMinification: boolean;
  enableTreeShaking: boolean;
  enableCodeSplitting: boolean;
  enablePerformanceOptimization: boolean;
  targetBundleSize: number; // KB
}

// 质量检查配置接口
export interface QualityCheckConfig {
  enableTypeScript: boolean;
  enableESLint: boolean;
  enablePrettier: boolean;
  enableThreeJSValidation: boolean;
  strictMode: boolean;
}

/**
 * 代码生成配置管理器
 */
export class CodeGenerationConfigManager {
  private static instance: CodeGenerationConfigManager;
  private config: CodeGenerationConfig;
  private optimizationConfig: OptimizationConfig;
  private qualityConfig: QualityCheckConfig;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.optimizationConfig = this.getDefaultOptimizationConfig();
    this.qualityConfig = this.getDefaultQualityConfig();
  }

  static getInstance(): CodeGenerationConfigManager {
    if (!CodeGenerationConfigManager.instance) {
      CodeGenerationConfigManager.instance = new CodeGenerationConfigManager();
    }
    return CodeGenerationConfigManager.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): CodeGenerationConfig {
    return {
      framework: "three.js",
      outputFormat: "tsx",
      includeAssets: true,
      minify: false,
      enableOptimization: true,
      enableTypeChecking: true,
      maxTokens: 20000,
      temperature: 0.1
    };
  }

  /**
   * 获取默认优化配置
   */
  private getDefaultOptimizationConfig(): OptimizationConfig {
    return {
      enableMinification: false,
      enableTreeShaking: true,
      enableCodeSplitting: false,
      enablePerformanceOptimization: true,
      targetBundleSize: 500 // 500KB
    };
  }

  /**
   * 获取默认质量检查配置
   */
  private getDefaultQualityConfig(): QualityCheckConfig {
    return {
      enableTypeScript: true,
      enableESLint: true,
      enablePrettier: true,
      enableThreeJSValidation: true,
      strictMode: true
    };
  }

  /**
   * 获取当前配置
   */
  getConfig(): CodeGenerationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CodeGenerationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取优化配置
   */
  getOptimizationConfig(): OptimizationConfig {
    return { ...this.optimizationConfig };
  }

  /**
   * 更新优化配置
   */
  updateOptimizationConfig(newConfig: Partial<OptimizationConfig>): void {
    this.optimizationConfig = { ...this.optimizationConfig, ...newConfig };
  }

  /**
   * 获取质量检查配置
   */
  getQualityConfig(): QualityCheckConfig {
    return { ...this.qualityConfig };
  }

  /**
   * 更新质量检查配置
   */
  updateQualityConfig(newConfig: Partial<QualityCheckConfig>): void {
    this.qualityConfig = { ...this.qualityConfig, ...newConfig };
  }

  /**
   * 重置为默认配置
   */
  resetToDefaults(): void {
    this.config = this.getDefaultConfig();
    this.optimizationConfig = this.getDefaultOptimizationConfig();
    this.qualityConfig = this.getDefaultQualityConfig();
  }

  /**
   * 验证配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证基础配置
    if (this.config.maxTokens < 1000 || this.config.maxTokens > 50000) {
      errors.push('maxTokens must be between 1000 and 50000');
    }

    if (this.config.temperature < 0 || this.config.temperature > 2) {
      errors.push('temperature must be between 0 and 2');
    }

    // 验证优化配置
    if (this.optimizationConfig.targetBundleSize < 100) {
      errors.push('targetBundleSize must be at least 100KB');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取Three.js依赖列表
   */
  getThreeJSDependencies(): string[] {
    const baseDeps = ['three', '@types/three'];
    
    if (this.config.includeAssets) {
      baseDeps.push(
        'three/examples/jsm/controls/OrbitControls.js',
        'three/examples/jsm/loaders/FBXLoader.js',
        'three/examples/jsm/loaders/GLTFLoader.js',
        'three/examples/jsm/loaders/TextureLoader.js'
      );
    }

    return baseDeps;
  }

  /**
   * 获取生成统计信息
   */
  getGenerationStats(): {
    totalGenerations: number;
    successRate: number;
    averageTokens: number;
    averageTime: number;
  } {
    // 这里应该从实际的统计数据中获取
    // 目前返回模拟数据
    return {
      totalGenerations: 0,
      successRate: 0,
      averageTokens: 0,
      averageTime: 0
    };
  }

  /**
   * 导出配置为JSON
   */
  exportConfig(): string {
    return JSON.stringify({
      config: this.config,
      optimizationConfig: this.optimizationConfig,
      qualityConfig: this.qualityConfig
    }, null, 2);
  }

  /**
   * 从JSON导入配置
   */
  importConfig(jsonConfig: string): boolean {
    try {
      const parsed = JSON.parse(jsonConfig);
      
      if (parsed.config) {
        this.config = { ...this.config, ...parsed.config };
      }
      
      if (parsed.optimizationConfig) {
        this.optimizationConfig = { ...this.optimizationConfig, ...parsed.optimizationConfig };
      }
      
      if (parsed.qualityConfig) {
        this.qualityConfig = { ...this.qualityConfig, ...parsed.qualityConfig };
      }

      const validation = this.validateConfig();
      return validation.valid;
    } catch (error) {
      console.error('Failed to import config:', error);
      return false;
    }
  }

  /**
   * 获取环境特定配置
   */
  getEnvironmentConfig(environment: 'development' | 'production' | 'test'): Partial<CodeGenerationConfig> {
    switch (environment) {
      case 'development':
        return {
          minify: false,
          enableOptimization: false,
          temperature: 0.2
        };
      case 'production':
        return {
          minify: true,
          enableOptimization: true,
          temperature: 0.1
        };
      case 'test':
        return {
          minify: false,
          enableOptimization: false,
          maxTokens: 10000,
          temperature: 0.0
        };
      default:
        return {};
    }
  }

  /**
   * 应用环境配置
   */
  applyEnvironmentConfig(environment: 'development' | 'production' | 'test'): void {
    const envConfig = this.getEnvironmentConfig(environment);
    this.updateConfig(envConfig);
  }
}
