/**
 * Checkpoint API路由
 * 提供checkpoint的创建、读取、删除等操作
 */

import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import { 
  Checkpoint, 
  CheckpointStore, 
  CreateCheckpointOptions,
  RollbackCheckpointOptions,
  CheckpointOperationResult,
  CHECKPOINT_CONFIG 
} from '../../../src/types/CheckpointTypes';
import { checkpointUtils } from '../../../src/utils/CheckpointUtils';

const CHECKPOINTS_FILE = path.join(process.cwd(), CHECKPOINT_CONFIG.STORAGE_FILE);

/**
 * 确保数据目录存在
 */
async function ensureDataDir(): Promise<void> {
  const dataDir = path.dirname(CHECKPOINTS_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

/**
 * 读取checkpoint存储
 */
async function readCheckpointStore(): Promise<CheckpointStore> {
  try {
    await ensureDataDir();
    const data = await fs.readFile(CHECKPOINTS_FILE, 'utf-8');
    const store = JSON.parse(data) as CheckpointStore;
    
    // 验证存储结构
    if (!store.checkpoints || !Array.isArray(store.checkpoints)) {
      console.warn('[Checkpoint API] 存储结构不正确，使用默认结构');
      return checkpointUtils.createDefaultStore();
    }
    
    return store;
  } catch (error) {
    console.log('[Checkpoint API] 创建新的checkpoint存储');
    return checkpointUtils.createDefaultStore();
  }
}

/**
 * 写入checkpoint存储
 */
async function writeCheckpointStore(store: CheckpointStore): Promise<void> {
  await ensureDataDir();
  
  // 更新元数据
  store.lastUpdated = new Date().toISOString();
  store.metadata.totalCount = store.checkpoints.length;
  
  // 自动清理
  if (store.metadata.autoCleanup && 
      store.checkpoints.length > store.metadata.maxCount * CHECKPOINT_CONFIG.AUTO_CLEANUP_THRESHOLD) {
    store.checkpoints = checkpointUtils.cleanupOldCheckpoints(
      store.checkpoints, 
      store.metadata.maxCount
    );
    store.metadata.totalCount = store.checkpoints.length;
  }
  
  await fs.writeFile(CHECKPOINTS_FILE, JSON.stringify(store, null, 2), 'utf-8');
}

/**
 * GET - 获取所有checkpoint或特定checkpoint
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const checkpointId = searchParams.get('id');
    
    console.log('[Checkpoint API] 获取checkpoint:', checkpointId || '全部');
    
    const store = await readCheckpointStore();
    
    if (checkpointId) {
      // 获取特定checkpoint
      const checkpoint = store.checkpoints.find(cp => cp.id === checkpointId);
      
      if (!checkpoint) {
        return NextResponse.json({
          success: false,
          error: 'Checkpoint不存在'
        }, { status: 404 });
      }
      
      return NextResponse.json({
        success: true,
        data: { checkpoint }
      });
    } else {
      // 获取所有checkpoint
      return NextResponse.json({
        success: true,
        data: {
          checkpoints: store.checkpoints,
          currentCheckpointId: store.currentCheckpointId,
          totalCount: store.metadata.totalCount,
          lastUpdated: store.lastUpdated
        }
      });
    }
    
  } catch (error) {
    console.error('[Checkpoint API] 获取checkpoint失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

/**
 * POST - 创建新checkpoint或回滚到指定checkpoint
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { action } = body;
    
    if (action === 'create') {
      return await createCheckpoint(body as CreateCheckpointOptions & { action: string });
    } else if (action === 'rollback') {
      return await rollbackCheckpoint(body as RollbackCheckpointOptions & { action: string });
    } else {
      return NextResponse.json({
        success: false,
        error: '无效的操作类型'
      }, { status: 400 });
    }
    
  } catch (error) {
    console.error('[Checkpoint API] POST操作失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

/**
 * 创建新checkpoint
 */
async function createCheckpoint(options: CreateCheckpointOptions & { action: string; state: any }): Promise<NextResponse> {
  const { messageId, description, userAction, generatedScripts = [], isImportant = false, tags = [], state } = options;

  // 参数验证
  if (!messageId || !description || !userAction || !state) {
    return NextResponse.json({
      success: false,
      error: '缺少必要参数：messageId、description、userAction或state'
    }, { status: 400 });
  }

  // 验证messageId格式
  if (typeof messageId !== 'string' || messageId.length < 5) {
    return NextResponse.json({
      success: false,
      error: 'messageId格式不正确'
    }, { status: 400 });
  }

  // 验证state结构
  if (!state.scripts || !state.scene || !state.messages || !state.session) {
    return NextResponse.json({
      success: false,
      error: 'state数据结构不完整'
    }, { status: 400 });
  }
  
  console.log(`[Checkpoint API] 创建checkpoint: ${description}`);

  const store = await readCheckpointStore();

  // 检查是否已存在相同messageId的checkpoint
  const existingCheckpoint = store.checkpoints.find(cp => cp.messageId === messageId);
  if (existingCheckpoint) {
    console.warn(`[Checkpoint API] 已存在相同messageId的checkpoint: ${messageId}`);
    return NextResponse.json({
      success: false,
      error: '该消息已有对应的版本点'
    }, { status: 409 });
  }

  // 检查存储限制
  if (store.checkpoints.length >= store.metadata.maxCount) {
    console.log(`[Checkpoint API] 达到存储限制，执行自动清理`);
    // 自动清理将在writeCheckpointStore中执行
  }
  
  const checkpoint: Checkpoint = {
    id: checkpointUtils.generateCheckpointId(),
    version: checkpointUtils.generateVersion(store.checkpoints),
    messageId,
    timestamp: new Date().toISOString(),
    description,
    state,
    metadata: {
      userAction,
      generatedScripts,
      appliedChanges: [], // 将在后续更新
      creationType: 'auto',
      isImportant,
      tags
    }
  };
  
  // 验证checkpoint
  if (!checkpointUtils.validateCheckpoint(checkpoint)) {
    return NextResponse.json({
      success: false,
      error: 'Checkpoint数据验证失败'
    }, { status: 400 });
  }
  
  // 添加到存储
  store.checkpoints.push(checkpoint);
  store.currentCheckpointId = checkpoint.id;
  
  await writeCheckpointStore(store);
  
  console.log(`[Checkpoint API] 成功创建checkpoint: ${checkpoint.version}`);
  
  return NextResponse.json({
    success: true,
    message: 'Checkpoint创建成功',
    data: { checkpoint }
  });
}

/**
 * 回滚到指定checkpoint
 */
async function rollbackCheckpoint(options: RollbackCheckpointOptions & { action: string }): Promise<NextResponse> {
  const { checkpointId, clearSubsequent = true, force = false } = options;
  
  if (!checkpointId) {
    return NextResponse.json({
      success: false,
      error: '缺少checkpointId参数'
    }, { status: 400 });
  }
  
  console.log(`[Checkpoint API] 回滚到checkpoint: ${checkpointId}`);
  
  const store = await readCheckpointStore();
  const targetCheckpoint = store.checkpoints.find(cp => cp.id === checkpointId);
  
  if (!targetCheckpoint) {
    return NextResponse.json({
      success: false,
      error: 'Checkpoint不存在'
    }, { status: 404 });
  }
  
  // 如果需要清除后续checkpoint
  if (clearSubsequent) {
    const targetIndex = store.checkpoints.findIndex(cp => cp.id === checkpointId);
    const targetTimestamp = new Date(targetCheckpoint.timestamp).getTime();
    
    // 移除所有在目标checkpoint之后创建的checkpoint
    store.checkpoints = store.checkpoints.filter(cp => {
      const cpTimestamp = new Date(cp.timestamp).getTime();
      return cpTimestamp <= targetTimestamp;
    });
  }
  
  // 更新当前checkpoint
  store.currentCheckpointId = checkpointId;

  await writeCheckpointStore(store);

  // 同时更新chat-history.json，恢复到checkpoint时的消息状态
  await updateChatHistoryForRollback(targetCheckpoint);

  console.log(`[Checkpoint API] 成功回滚到checkpoint: ${targetCheckpoint.version}`);

  return NextResponse.json({
    success: true,
    message: '回滚成功',
    data: {
      checkpoint: targetCheckpoint,
      restoredState: targetCheckpoint.state
    }
  });
}

/**
 * DELETE - 删除指定checkpoint
 */
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const checkpointId = searchParams.get('id');
    
    if (!checkpointId) {
      return NextResponse.json({
        success: false,
        error: '缺少checkpoint ID'
      }, { status: 400 });
    }
    
    console.log(`[Checkpoint API] 删除checkpoint: ${checkpointId}`);
    
    const store = await readCheckpointStore();
    const checkpointIndex = store.checkpoints.findIndex(cp => cp.id === checkpointId);
    
    if (checkpointIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'Checkpoint不存在'
      }, { status: 404 });
    }
    
    const deletedCheckpoint = store.checkpoints[checkpointIndex];
    store.checkpoints.splice(checkpointIndex, 1);
    
    // 如果删除的是当前checkpoint，重置当前checkpoint
    if (store.currentCheckpointId === checkpointId) {
      store.currentCheckpointId = store.checkpoints.length > 0 ? store.checkpoints[store.checkpoints.length - 1].id : null;
    }
    
    await writeCheckpointStore(store);
    
    console.log(`[Checkpoint API] 成功删除checkpoint: ${deletedCheckpoint.version}`);
    
    return NextResponse.json({
      success: true,
      message: 'Checkpoint删除成功',
      data: {
        deletedCheckpoint,
        remainingCount: store.checkpoints.length
      }
    });
    
  } catch (error) {
    console.error('[Checkpoint API] 删除checkpoint失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

/**
 * 回滚时更新聊天历史
 * 根据checkpoint中保存的消息状态更新chat-history.json
 */
async function updateChatHistoryForRollback(checkpoint: Checkpoint): Promise<void> {
  try {
    const chatHistoryPath = path.join(process.cwd(), 'data', 'chat-history.json');

    // 读取当前聊天历史
    let chatHistory: Array<{
      sessionId: string;
      projectName: string;
      messages: Array<{
        id: string;
        type: string;
        content: string;
        timestamp: string;
        isStreaming?: boolean;
      }>;
      lastUpdated: string;
      totalMessages: number;
    }> = [];

    if (fsSync.existsSync(chatHistoryPath)) {
      const content = await fs.readFile(chatHistoryPath, 'utf-8');
      chatHistory = JSON.parse(content);
    }

    // 找到当前会话
    const currentSessionId = checkpoint.state.session.sessionId;
    const sessionIndex = chatHistory.findIndex(session => session.sessionId === currentSessionId);

    if (sessionIndex !== -1) {
      // 更新会话的消息为checkpoint中保存的状态
      const restoredMessages = checkpoint.state.messages.map((msg: Record<string, unknown>) => ({
        id: msg.id as string,
        type: msg.type as string,
        content: msg.content as string,
        timestamp: msg.timestamp as string,
        isStreaming: msg.isStreaming as boolean | undefined
      }));

      chatHistory[sessionIndex] = {
        ...chatHistory[sessionIndex],
        messages: restoredMessages,
        lastUpdated: new Date().toISOString(),
        totalMessages: restoredMessages.length
      };

      console.log(`[Checkpoint API] 更新聊天历史: 从 ${chatHistory[sessionIndex].totalMessages} 条消息恢复到 ${restoredMessages.length} 条`);
    } else {
      console.warn(`[Checkpoint API] 未找到会话 ${currentSessionId}，跳过聊天历史更新`);
    }

    // 写回文件
    await fs.writeFile(chatHistoryPath, JSON.stringify(chatHistory, null, 2), 'utf-8');
    console.log('[Checkpoint API] 聊天历史已更新');

  } catch (error) {
    console.error('[Checkpoint API] 更新聊天历史失败:', error);
    // 不抛出错误，避免影响主要的回滚流程
  }
}
