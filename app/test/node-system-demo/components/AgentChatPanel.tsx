/**
 * AgentChatPanel - AI Agent对话面板组件
 * 处理与AI Agent的对话交互功能
 */

'use client';

import React, { useEffect, useCallback, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import type { AgentMessage } from '../contexts/NodeSystemContext';

interface AgentChatPanelProps {
  className?: string;
}

export const AgentChatPanel: React.FC<AgentChatPanelProps> = ({ className = '' }) => {
  const {
    agentMessages,
    agentInput,
    isAgentProcessing,
    isLoadingHistory,
    currentSessionId,
    nodes,
    selectedNode,
    setAgentInput,
    setIsAgentProcessing,
    addAgentMessage,
    setAgentMessages,
    setIsLoadingHistory,
    setCurrentSessionId,
    generateSessionId,
    scrollToBottom,
    addScriptFile,
    createCheckpoint,
    checkpoints,
    rollbackToCheckpoint,
    isRollingBack,
    loadCheckpoints
  } = useNodeSystem();

  // 回滚确认对话框状态
  const [showRollbackDialog, setShowRollbackDialog] = useState(false);
  const [rollbackCheckpointId, setRollbackCheckpointId] = useState<string | null>(null);

  // 处理回滚点击
  const handleRollbackClick = useCallback((checkpointId: string) => {
    setRollbackCheckpointId(checkpointId);
    setShowRollbackDialog(true);
  }, []);

  // 确认回滚
  const handleRollbackConfirm = useCallback(async () => {
    if (!rollbackCheckpointId) return;

    const success = await rollbackToCheckpoint(rollbackCheckpointId);
    if (success) {
      setShowRollbackDialog(false);
      setRollbackCheckpointId(null);
    }
  }, [rollbackCheckpointId, rollbackToCheckpoint]);

  // 取消回滚
  const handleRollbackCancel = useCallback(() => {
    setShowRollbackDialog(false);
    setRollbackCheckpointId(null);
  }, []);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const saveHistoryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUserScrollingRef = useRef(false);
  const shouldAutoScrollRef = useRef(true);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 智能滚动处理
  const handleScroll = useCallback(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    // 如果用户滚动到接近底部，启用自动滚动
    if (isNearBottom) {
      shouldAutoScrollRef.current = true;
      isUserScrollingRef.current = false;
    } else {
      // 用户主动滚动到其他位置，暂停自动滚动
      shouldAutoScrollRef.current = false;
      isUserScrollingRef.current = true;
    }

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置超时，如果用户停止滚动一段时间后重新启用自动滚动
    scrollTimeoutRef.current = setTimeout(() => {
      isUserScrollingRef.current = false;
    }, 1000);
  }, []);

  // 自动滚动到底部
  const autoScrollToBottom = useCallback(() => {
    if (shouldAutoScrollRef.current && !isUserScrollingRef.current) {
      const container = messagesContainerRef.current;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, []);

  // 在消息更新时自动滚动
  useEffect(() => {
    autoScrollToBottom();
  }, [agentMessages, autoScrollToBottom]);

  // 加载对话历史
  const loadChatHistory = useCallback(async () => {
    try {
      setIsLoadingHistory(true);
      const sessionId = generateSessionId();
      setCurrentSessionId(sessionId);
      
      console.log(`[对话历史] 加载会话: ${sessionId}`);
      
      const response = await fetch(`/api/chat-history?sessionId=${sessionId}`);
      const result = await response.json();
      
      if (result.success && result.data.messages) {
        const messagesWithDates = result.data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        
        setAgentMessages(messagesWithDates);
        console.log(`[对话历史] 成功加载 ${messagesWithDates.length} 条消息`);
      } else {
        console.warn('[对话历史] 加载失败:', result.error);
      }
    } catch (error) {
      console.error('[对话历史] 加载时发生错误:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [generateSessionId, setCurrentSessionId, setAgentMessages, setIsLoadingHistory]);

  // 保存对话历史
  const saveChatHistory = useCallback(async () => {
    if (agentMessages.length === 0 || !currentSessionId) return;
    
    try {
      const response = await fetch('/api/chat-history', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: currentSessionId,
          messages: agentMessages
        })
      });
      
      const result = await response.json();
      if (!result.success) {
        console.warn('[对话历史] 保存失败:', result.error);
      }
    } catch (error) {
      console.error('[对话历史] 保存时发生错误:', error);
    }
  }, [agentMessages, currentSessionId]);

  // 清空对话历史
  const clearChatHistory = useCallback(async () => {
    if (!currentSessionId) return;
    
    try {
      const response = await fetch('/api/chat-history', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: currentSessionId })
      });
      
      const result = await response.json();
      if (result.success) {
        setAgentMessages([]);
        console.log('[对话历史] 历史已清空');
      } else {
        console.warn('[对话历史] 清空失败:', result.error);
      }
    } catch (error) {
      console.error('[对话历史] 清空时发生错误:', error);
    }
  }, [currentSessionId, setAgentMessages]);

  // 发送消息给Agent
  const sendMessageToAgent = useCallback(async () => {
    if (!agentInput.trim() || isAgentProcessing) return;

    const userMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'user' as const,
      content: agentInput.trim(),
      timestamp: new Date()
    };

    addAgentMessage(userMessage);
    setAgentInput('');
    setIsAgentProcessing(true);

    // 创建checkpoint - 保存用户发送消息前的状态
    let checkpoint = null;
    try {
      console.log('[Agent对话] 创建checkpoint:', userMessage.content);
      checkpoint = await createCheckpoint(
        userMessage.id,
        userMessage.content,
        userMessage.content
      );
      if (checkpoint) {
        console.log('[Agent对话] Checkpoint创建成功:', checkpoint.version);
      } else {
        console.warn('[Agent对话] Checkpoint创建失败：返回null');
        // 添加警告消息给用户
        const warningMessage = {
          id: `warning_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          type: 'agent' as const,
          content: '⚠️ 版本保存失败，但脚本生成将继续进行。建议稍后手动保存重要变更。',
          timestamp: new Date()
        };
        addAgentMessage(warningMessage);
      }
    } catch (checkpointError) {
      console.warn('[Agent对话] Checkpoint创建失败:', checkpointError);
      // 添加错误消息给用户
      const errorMessage = {
        id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        type: 'agent' as const,
        content: `❌ 版本保存失败：${checkpointError instanceof Error ? checkpointError.message : '未知错误'}。脚本生成将继续进行。`,
        timestamp: new Date()
      };
      addAgentMessage(errorMessage);
    }

    try {
      console.log('[Agent对话] 发送脚本生成请求:', userMessage.content);

      // 调用流式脚本生成API
      const response = await fetch('/api/script-generation-stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userRequirement: userMessage.content,
          sceneContext: {
            nodes: nodes.map(node => ({
              id: node.id,
              name: node.name,
              type: node.type,
              position: node.position,
              rotation: node.rotation
            })),
            selectedNode: selectedNode ? {
              id: selectedNode.id,
              name: selectedNode.name,
              type: selectedNode.type,
              position: selectedNode.position,
              rotation: selectedNode.rotation
            } : null,
            totalNodes: nodes.length
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('响应体为空');
      }

      // 创建流式响应消息
      const streamMessageId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${Math.floor(Math.random() * 10000)}`;
      let streamContent = '';
      let scriptGenerated = false;
      let generatedScript: any = null;

      const streamMessage = {
        id: streamMessageId,
        type: 'agent' as const,
        content: '🔧 开始生成脚本...\n\n',
        timestamp: new Date(),
        isStreaming: true
      };

      addAgentMessage(streamMessage);

      // 创建更新消息内容的函数
      const updateStreamMessage = (newContent: string, isComplete = false) => {
        const updatedMessage = {
          id: streamMessageId,
          type: 'agent' as const,
          content: newContent,
          timestamp: new Date(),
          isStreaming: !isComplete
        };

        // 更新消息列表中的对应消息
        setAgentMessages(prevMessages => {
          const messageExists = prevMessages.some(msg => msg.id === streamMessageId);
          if (messageExists) {
            return prevMessages.map(msg =>
              msg.id === streamMessageId ? updatedMessage : msg
            );
          } else {
            // 如果消息不存在，添加它（这种情况不应该发生，但作为安全措施）
            console.warn('[Agent对话] 尝试更新不存在的消息:', streamMessageId);
            return [...prevMessages, updatedMessage];
          }
        });
      };

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = ''; // 用于累积不完整的数据

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true });

        // 按行分割数据
        const lines = buffer.split('\n');

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue; // 跳过空行

          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim();
              if (jsonStr === '') continue; // 跳过空数据

              const data = JSON.parse(jsonStr);
              console.log('[Agent对话] 收到流式数据:', data);

              if (data.type === 'token') {
                streamContent += data.data;
                // 实时更新流式消息内容
                updateStreamMessage(`🔧 正在生成脚本...\n\n\`\`\`javascript\n${streamContent}\n\`\`\``);
              } else if (data.type === 'status') {
                console.log('[Agent对话] 状态更新:', data.data);
                updateStreamMessage(`🔧 ${data.data}\n\n${streamContent ? `\`\`\`javascript\n${streamContent}\n\`\`\`` : ''}`);
              } else if (data.type === 'complete') {
                console.log('[Agent对话] 脚本生成完成');
                scriptGenerated = true;
                generatedScript = data.data?.generatedScript || data.data;
                console.log('[Agent对话] 解析的脚本数据:', generatedScript);
              }
            } catch (error) {
              console.warn('[Agent对话] 解析流式数据失败:', line, error);
            }
          }
        }
      }

      // 更新最终消息内容并保存脚本
      if (scriptGenerated && generatedScript) {
        console.log('[Agent对话] 开始保存脚本:', generatedScript);

        // 先保存脚本文件
        try {
          const saveResponse = await fetch('/api/save-script', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              scriptId: generatedScript.id,
              scriptName: generatedScript.name,
              scriptContent: generatedScript.content,
              metadata: generatedScript.metadata
            })
          });

          const saveResult = await saveResponse.json();
          console.log('[Agent对话] 脚本文件保存结果:', saveResult);

          // 保存到persistent-features.json
          const persistentResponse = await fetch('/api/persistent-features', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              id: generatedScript.id,
              name: generatedScript.name,
              description: userMessage.content,
              scriptContent: generatedScript.content
            })
          });

          const persistentResult = await persistentResponse.json();
          console.log('[Agent对话] 持久化保存结果:', persistentResult);

        } catch (saveError) {
          console.error('[Agent对话] 保存脚本失败:', saveError);
        }

        const finalContent = `✅ 已生成脚本："${generatedScript?.name || 'generated_script'}"！

**生成内容摘要:**
- 脚本类型: ${generatedScript?.metadata?.functionType || 'utility'}
- 目标节点: ${generatedScript?.metadata?.targetNodeTypes?.join(', ') || 'mesh'}
- 功能: ${userMessage.content}
- 依赖项: Three.js

📁 脚本已自动保存并应用到场景中。`;

        // 更新消息为最终状态
        updateStreamMessage(finalContent, true);

        // 添加脚本到ScriptManager（直接激活状态）
        const scriptFile = {
          id: generatedScript.id,
          name: generatedScript.name,
          content: generatedScript.content,
          nodeId: generatedScript.nodeId,
          createdAt: new Date(generatedScript.createdAt || Date.now()),
          lastModified: new Date(generatedScript.lastModified || Date.now()),
          isActive: true, // 直接设置为激活状态
          isPreview: false, // 不是预览状态
          metadata: {
            description: generatedScript.metadata?.description || userMessage.content,
            targetNodeTypes: generatedScript.metadata?.targetNodeTypes || ['mesh'],
            dependencies: generatedScript.metadata?.dependencies || ['three'],
            functionType: generatedScript.metadata?.functionType || 'utility'
          }
        };

        addScriptFile(scriptFile);

        // 自动执行脚本（持久化应用）
        setTimeout(() => {
          // 触发脚本执行
          const executeEvent = new CustomEvent('executeScript', {
            detail: { scriptId: scriptFile.id }
          });
          window.dispatchEvent(executeEvent);
        }, 100);

        // 更新checkpoint状态（如果checkpoint创建成功）
        if (checkpoint) {
          try {
            // 这里可以更新checkpoint的metadata，记录生成的脚本
            console.log('[Agent对话] 脚本已应用，checkpoint:', checkpoint.version);
          } catch (error) {
            console.warn('[Agent对话] 更新checkpoint失败:', error);
          }
        }

        // 添加成功应用消息
        setTimeout(() => {
          const successMessage = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            type: 'agent' as const,
            content: `✅ 脚本 "${generatedScript.name}" 已自动应用到场景中！您可以立即看到效果。`,
            timestamp: new Date()
          };

          addAgentMessage(successMessage);
        }, 500);
      } else {
        updateStreamMessage(streamContent || '脚本生成完成，但内容为空。', true);
      }



    } catch (error) {
      console.error('[Agent对话] 发送消息失败:', error);

      const errorMessage = {
        id: `error_${Date.now()}`,
        type: 'agent' as const,
        content: `抱歉，处理您的请求时出现了错误：${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: new Date()
      };

      addAgentMessage(errorMessage);
    } finally {
      setIsAgentProcessing(false);
    }
  }, [agentInput, isAgentProcessing, currentSessionId, agentMessages, addAgentMessage, setAgentInput, setIsAgentProcessing, addScriptFile, setAgentMessages, createCheckpoint, nodes, selectedNode]);

  // 处理键盘事件
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessageToAgent();
    }
  }, [sendMessageToAgent]);

  // 组件挂载时加载历史和checkpoints
  useEffect(() => {
    loadChatHistory();
    loadCheckpoints();
  }, [loadChatHistory, loadCheckpoints]);

  // 监听消息变化并自动保存
  useEffect(() => {
    if (agentMessages.length > 0 && !isLoadingHistory) {
      // 延迟保存，避免频繁调用
      if (saveHistoryTimeoutRef.current) {
        clearTimeout(saveHistoryTimeoutRef.current);
      }
      
      saveHistoryTimeoutRef.current = setTimeout(() => {
        saveChatHistory();
      }, 1000);
    }
    
    return () => {
      if (saveHistoryTimeoutRef.current) {
        clearTimeout(saveHistoryTimeoutRef.current);
      }
    };
  }, [agentMessages, isLoadingHistory, saveChatHistory]);

  // 监听消息变化并自动滚动
  useEffect(() => {
    if (agentMessages.length > 0) {
      setTimeout(scrollToBottom, 50);
    }
  }, [agentMessages, scrollToBottom]);

  return (
    <div className={`w-[480px] bg-white border-r border-gray-200 flex flex-col ${className}`}>
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-bold mb-2 text-gray-900">🤖 Ignis Agent</h2>
            <p className="text-sm text-gray-600">Enjoy creating games with vibe coding</p>
          </div>
          {currentSessionId && (
            <div className="text-right">
              <div className="text-xs text-gray-500">
                会话: {currentSessionId.split('_')[1]}
              </div>
              <div className="text-xs text-gray-500">
                消息: {agentMessages.length}
              </div>
              <button
                onClick={clearChatHistory}
                className="text-xs text-red-500 hover:text-red-700 mt-1"
                title="清空对话历史"
              >
                清空历史
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* 消息列表 */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        onScroll={handleScroll}
      >
        {isLoadingHistory ? (
          <div className="flex justify-center items-center h-32">
            <div className="flex items-center space-x-2 text-gray-500">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">加载对话历史...</span>
            </div>
          </div>
        ) : agentMessages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <p className="text-sm">还没有对话记录</p>
            <p className="text-xs mt-1">开始与Agent对话吧！</p>
          </div>
        ) : (
          agentMessages.map((message) => {
            // 查找与此消息关联的checkpoint
            const relatedCheckpoint = message.type === 'user'
              ? checkpoints.find(cp => cp.messageId === message.id)
              : null;

            return (
              <div key={message.id} className="space-y-2">
                {/* 消息内容 */}
                <div className={`flex items-start gap-2 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  {/* 回滚图标 - 只在用户消息左边显示 */}
                  {message.type === 'user' && relatedCheckpoint && (
                    <button
                      onClick={() => handleRollbackClick(relatedCheckpoint.id)}
                      disabled={isRollingBack}
                      className="flex-shrink-0 mt-1 p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="回滚到此消息发送时的状态"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                        />
                      </svg>
                    </button>
                  )}

                  <div className={`max-w-[80%] rounded-lg px-3 py-2 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    {message.type === 'agent' ? (
                      <div className="prose max-w-none overflow-hidden text-sm">
                        <ReactMarkdown
                          components={{
                            code: ({ children, className, ...props }) => {
                              if (!className) {
                                return (
                                  <code className="bg-gray-200 px-1 py-0.5 rounded text-xs font-mono break-all" {...props}>
                                    {children}
                                  </code>
                                )
                              }
                              return (
                                <pre className="bg-gray-50 text-gray-900 p-3 rounded-lg overflow-x-auto text-xs border">
                                  <code className="break-all whitespace-pre-wrap">{children}</code>
                                </pre>
                              )
                            }
                          }}
                        >
                          {message.content}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      <div className="text-sm whitespace-pre-wrap break-words">
                        {message.content}
                      </div>
                    )}
                    <div className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString()}
                      {message.isStreaming && (
                        <span className="ml-2 inline-flex items-center">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <span className="ml-1">生成中...</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>


              </div>
            );
          })
        )}
        
        {isAgentProcessing && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-3 py-2 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-100"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-200"></div>
                <span className="text-gray-700">Agent正在思考...</span>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            value={agentInput}
            onChange={(e) => setAgentInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入消息... (Enter发送，Shift+Enter换行)"
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={2}
            disabled={isAgentProcessing}
          />
          <button
            onClick={sendMessageToAgent}
            disabled={!agentInput.trim() || isAgentProcessing}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            {isAgentProcessing ? '发送中...' : '发送'}
          </button>
        </div>
      </div>

      {/* 回滚确认对话框 */}
      {showRollbackDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              确认回滚操作
            </h3>
            <p className="text-gray-600 mb-4">
              您确定要回滚到此消息发送时的状态吗？
            </p>
            <p className="text-sm text-yellow-700 bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
              ⚠️ <strong>警告：</strong>此操作将清除该消息之后生成的所有内容，包括脚本、场景变更和对话记录。此操作无法撤销。
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={handleRollbackCancel}
                disabled={isRollingBack}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors disabled:opacity-50"
              >
                取消
              </button>
              <button
                onClick={handleRollbackConfirm}
                disabled={isRollingBack}
                className="px-4 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRollingBack ? '回滚中...' : '确认回滚'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentChatPanel;
