"use client"

import React, { useState, useEffect, use<PERSON>allback, useRef } from "react"
import { useSearchParams } from 'next/navigation'
import ThreeCanvas from "@/src/three/components/ThreeCanvas"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import ReactMarkdown from 'react-markdown'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import {
  Play,
  Pause,
  RotateCcw,
  Download,
  Send,
  Bot,
  User,
  AlertCircle,
  Target,
  Maximize,
  Minimize,
  Palette,
  Code,
  ChevronDown,
  Monitor,
  Boxes,
  Package,
  FileCode,
  Upload,
  FolderOpen,
  Settings,
  Move3D,
  RotateCw,
  Scale,
} from "lucide-react"

// 开发环境检查
const isDevelopment = process.env.NODE_ENV === 'development'

// 开发环境日志工具
const devLog = (message: string, ...args: unknown[]) => {
  if (isDevelopment) {
    console.log(`[Canvas Dev] ${message}`, ...args)
  }
}

// 项目状态类型
type ProjectStatus = 'creating' | 'designing' | 'generating' | 'completed' | 'error'

// 游戏项目接口
interface GameProject {
  id: string
  name: string
  description: string
  status: ProjectStatus
  gameDesign?: Record<string, unknown>
  generatedCode?: Record<string, string>
  metadata: {
    totalIterations: number
    generationTime: number
    tokensUsed: number
    qualityScore: number
    templateUsed?: string
  }
}

// Agent角色类型
type AgentRole = 'designer' | 'coder' | 'system'

// 聊天消息接口
interface ChatMessage {
  id: string
  type: 'user' | 'agent'
  role?: AgentRole
  content: string
  timestamp: Date
  status?: 'sending' | 'sent' | 'error'
  isStreaming?: boolean
}

// 节点配置接口
interface GameNode {
  id: string
  name: string
  type: 'mesh' | 'camera' | 'light' | 'player' | 'enemy' | 'prop'
  position: { x: number, y: number, z: number }
  rotation: { x: number, y: number, z: number }
  scale: { x: number, y: number, z: number }
  model?: string
  material?: string
  animation?: string
  visible: boolean
}

// 资源文件接口
interface AssetFile {
  id: string
  name: string
  type: 'model' | 'animation' | 'material' | 'texture'
  path: string
  size: number
  uploadedAt: Date
}

export default function CanvasNewPage() {
  const searchParams = useSearchParams()
  const projectId = searchParams.get('projectId')

  // 项目ID验证函数
  const validateProjectId = useCallback((id: string | null): boolean => {
    if (!id) {
      console.error('[Canvas] 项目ID为空')
      return false
    }
    if (!id.startsWith('game_')) {
      console.error(`[Canvas] 项目ID格式错误: ${id}，应该以'game_'开头`)
      return false
    }
    // 检查ID格式：game_timestamp_random
    const parts = id.split('_')
    if (parts.length !== 3) {
      console.error(`[Canvas] 项目ID格式错误: ${id}，应该是'game_timestamp_random'格式`)
      return false
    }
    return true
  }, [])
  
  // 项目状态
  const [project, setProject] = useState<GameProject | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  
  // 游戏状态
  const [isPlaying, setIsPlaying] = useState(false)
  const [gameLoaded, setGameLoaded] = useState(false)
  const [gameComponent, setGameComponent] = useState<React.ComponentType | null>(null)
  const [componentLoadError, setComponentLoadError] = useState<string | null>(null)
  
  // UI状态
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [activeTab, setActiveTab] = useState("preview")

  // 聊天状态
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatInput, setChatInput] = useState("")
  const [isChatLoading, setIsChatLoading] = useState(false)

  // 流式输出状态
  const [currentPhase, setCurrentPhase] = useState<'design' | 'code' | 'complete'>('design')
  const [currentAgent, setCurrentAgent] = useState<AgentRole>('system')

  // 优化的流式输出状态 - 聚合同一次响应的所有内容
  const [currentStreamBuffer, setCurrentStreamBuffer] = useState("")
  const [isStreamingActive, setIsStreamingActive] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)

  // 节点和资源状态
  const [gameNodes, setGameNodes] = useState<GameNode[]>([])
  const [assets, setAssets] = useState<AssetFile[]>([])
  const [selectedNode, setSelectedNode] = useState<GameNode | null>(null)

  // 滚动区域引用
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const streamingMessageRefs = useRef<Map<string, HTMLDivElement>>(new Map())

  // 智能滚动状态
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)
  const userScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 检查是否接近底部
  const isNearBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        const { scrollTop, scrollHeight, clientHeight } = scrollContainer
        return scrollHeight - scrollTop - clientHeight < 100 // 距离底部100px内认为是接近底部
      }
    }
    return true
  }, [])

  // 处理用户滚动事件
  const handleUserScroll = useCallback(() => {
    const nearBottom = isNearBottom()

    if (!nearBottom) {
      setIsUserScrolling(true)
      setShowScrollToBottom(true)

      // 清除之前的定时器
      if (userScrollTimeoutRef.current) {
        clearTimeout(userScrollTimeoutRef.current)
      }

      // 2秒后如果用户没有继续滚动，检查是否回到底部
      userScrollTimeoutRef.current = setTimeout(() => {
        if (isNearBottom()) {
          setIsUserScrolling(false)
          setShowScrollToBottom(false)
        }
      }, 2000)
    } else {
      setIsUserScrolling(false)
      setShowScrollToBottom(false)
    }
  }, [isNearBottom])

  // 改进的自动滚动到底部函数
  const scrollToBottom = useCallback(() => {
    // 只有在用户没有主动滚动时才自动滚动
    if (!isUserScrolling) {
      // 方法1：使用messagesEndRef直接滚动到底部
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
        return
      }

      // 方法2：fallback到原有的ScrollArea方式
      if (scrollAreaRef.current) {
        const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight
        }
      }
    }
  }, [isUserScrolling])

  // 强制滚动到底部（用于"回到底部"按钮）
  const forceScrollToBottom = useCallback(() => {
    setIsUserScrolling(false)
    setShowScrollToBottom(false)

    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
    }
  }, [])

  // 当消息更新时自动滚动，添加防抖
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      scrollToBottom()
    }, 50) // 50ms防抖，确保DOM更新完成

    return () => clearTimeout(timeoutId)
  }, [chatMessages, currentStreamBuffer, scrollToBottom])

  // 滚动消息块内部内容到底部
  const scrollMessageContentToBottom = useCallback((messageId: string) => {
    const messageElement = streamingMessageRefs.current.get(messageId)
    if (messageElement) {
      messageElement.scrollTop = messageElement.scrollHeight
    }
  }, [])

  // 设置滚动事件监听器
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleUserScroll)
        return () => {
          scrollContainer.removeEventListener('scroll', handleUserScroll)
        }
      }
    }
  }, [handleUserScroll])

  // 流式输出时的实时滚动
  useEffect(() => {
    if (isStreamingActive && currentStreamBuffer) {
      scrollToBottom()
      // 如果有流式消息ID，也滚动该消息块内部内容
      if (streamingMessageId) {
        scrollMessageContentToBottom(streamingMessageId)
      }
    }
  }, [isStreamingActive, currentStreamBuffer, streamingMessageId, scrollToBottom, scrollMessageContentToBottom])

  // 自定义代码块组件，支持复制功能
  const CodeBlock = ({ children, className, ...props }: {
    children: React.ReactNode
    className?: string
    [key: string]: unknown
  }) => {
    const match = /language-(\w+)/.exec(className || '')
    const language = match ? match[1] : 'text'

    const [copied, setCopied] = useState(false)

    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(String(children))
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('复制失败:', err)
      }
    }

    return (
      <div className="relative group">
        <button
          onClick={copyToClipboard}
          className="absolute top-2 right-2 px-2 py-1 text-xs bg-gray-600 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity"
        >
          {copied ? '已复制' : '复制'}
        </button>
        <SyntaxHighlighter
          style={vscDarkPlus}
          language={language}
          PreTag="div"
          className="text-sm max-h-96 overflow-y-auto"
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      </div>
    )
  }

  // 检测内容类型
  const detectContentType = (content: string): 'json' | 'code' | 'text' => {
    const trimmed = content.trim()

    // 检测JSON格式
    if ((trimmed.startsWith('{') && trimmed.includes('"')) ||
        (trimmed.startsWith('[') && trimmed.includes('"'))) {
      return 'json'
    }

    // 检测代码格式（包含import、export、function等关键字）
    if (trimmed.includes('import ') || trimmed.includes('export ') ||
        trimmed.includes('function ') || trimmed.includes('const ') ||
        trimmed.includes('class ') || trimmed.includes('interface ')) {
      return 'code'
    }

    return 'text'
  }

  // 流式输出时的简单内容处理（不进行复杂格式化）
  const formatStreamingContentSimple = (content: string, phase: 'design' | 'code' | 'complete'): string => {
    if (!content) {
      return phase === 'design' ? '🤖 正在设计游戏...' :
             phase === 'code' ? '🔧 正在生成代码...' : '⚙️ 处理中...'
    }

    // 流式输出时直接返回原始内容，不进行复杂格式化
    return content
  }

  // 最终完成时的内容格式化函数
  const formatFinalContent = (content: string, phase: 'design' | 'code' | 'complete'): string => {
    if (!content) {
      return phase === 'design' ? '🤖 正在设计游戏...' :
             phase === 'code' ? '🔧 正在生成代码...' : '⚙️ 处理中...'
    }

    const contentType = detectContentType(content)

    if (phase === 'design') {
      if (contentType === 'json') {
        // JSON内容用代码块格式显示
        try {
          const parsed = JSON.parse(content)
          return '```json\n' + JSON.stringify(parsed, null, 2) + '\n```'
        } catch {
          // 如果解析失败，直接用代码块包装
          return '```json\n' + content + '\n```'
        }
      } else {
        // 普通文本内容，保持原样但清理多余空白
        return content.replace(/\s+/g, ' ').trim()
      }
    } else if (phase === 'code') {
      // 代码阶段：用代码块格式显示
      if (contentType === 'code') {
        return '```typescript\n' + content + '\n```'
      } else {
        return content
      }
    } else {
      // 其他阶段：直接返回内容
      return content
    }
  }

  // 动态加载游戏组件
  const loadGameComponent = useCallback(async (gameId: string) => {
    try {
      console.log(`[Canvas] 尝试加载游戏组件: ${gameId}`)
      setComponentLoadError(null) // 清除之前的错误

      // 验证gameId格式
      if (!gameId || !gameId.startsWith('game_')) {
        const error = `无效的游戏ID格式: ${gameId}`
        console.error(`[Canvas] ${error}`)
        setComponentLoadError(error)
        return false
      }

      // 检查组件文件是否存在
      const checkResponse = await fetch(`/api/check-component?gameId=${gameId}`)
      const checkResult = await checkResponse.json()

      if (!checkResult.exists) {
        const error = `组件文件不存在，游戏可能尚未生成完成`
        console.warn(`[Canvas] 组件文件不存在: ${gameId}，文件路径: ${checkResult.componentPath}`)
        console.warn(`[Canvas] 这通常意味着游戏生成尚未完成或生成过程中出现问题`)
        setComponentLoadError(error)
        return false
      }

      // 额外的安全检查：再次确认文件存在
      if (!checkResult.componentPath) {
        const error = `组件路径无效`
        console.error(`[Canvas] 组件路径无效: ${gameId}`)
        setComponentLoadError(error)
        return false
      }

      console.log(`[Canvas] 组件文件存在，开始动态导入: ${gameId}`)

      // 动态导入生成的组件
      try {
        console.log(`[Canvas] 尝试导入组件: ${gameId}`)
        
        // 验证 gameId 不是 undefined 或 null
        if (!gameId || gameId === 'unknown') {
          throw new Error(`Invalid gameId: ${gameId}`)
        }
        
        // 使用 Next.js 推荐的动态导入方式，添加错误处理
        const componentModule = await import(`@/components/generated/${gameId}`)
        const GameComponent = componentModule.default

        if (!GameComponent) {
          const error = `组件没有默认导出，可能是代码生成问题`
          console.warn(`[Canvas] 组件没有默认导出: ${gameId}`)
          setComponentLoadError(error)
          return false
        }

        console.log(`[Canvas] 成功导入游戏组件: ${gameId}`)

        // 设置游戏组件
        setGameComponent(() => GameComponent)
        setComponentLoadError(null) // 清除错误状态
        return true

      } catch (importError) {
        const error = `组件导入失败，可能是代码语法错误或依赖问题`
        console.error(`[Canvas] 动态导入失败: ${gameId}`, importError)
        console.error(`[Canvas] 这可能是因为组件代码有语法错误或依赖问题`)
        
        // 提供更详细的错误信息
        if (importError instanceof Error) {
          console.error(`[Canvas] 导入错误详情: ${importError.message}`)
          devLog(`导入错误详情`, { gameId, error: importError.message, stack: importError.stack })
          if (importError.message.includes('Cannot resolve module')) {
            console.error(`[Canvas] 模块解析失败，可能是文件路径或依赖问题`)
          }
        }
        setComponentLoadError(error)
        return false
      }

    } catch (error) {
      const errorMsg = `加载游戏组件时发生意外错误`
      console.error(`[Canvas] 加载游戏组件时发生意外错误 ${gameId}:`, error)
      setComponentLoadError(errorMsg)
      return false
    }
  }, [])

  // 获取场景准备回调函数
  const getSceneReadyCallback = useCallback(() => {
    if (gameComponent) {
      // 如果有生成的游戏组件，使用组件内部的场景初始化逻辑
      return () => {
        console.log('[Canvas] 使用生成的游戏组件初始化场景')
        // 生成的组件应该自己处理场景初始化
      }
    } else {
      // 否则使用默认的Three.js场景
      return () => {
        console.log('[Canvas] 使用默认Three.js场景')
        // TODO: 实现Three.js默认场景
      }
    }
  }, [gameComponent])

  // 加载项目数据
  const loadProject = useCallback(async (skipAutoGenerate: boolean = false) => {
    if (!validateProjectId(projectId)) {
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch(`/api/projects?id=${projectId}`)
      if (!response.ok) {
        throw new Error('获取项目失败')
      }
      
      const projectData = await response.json()
      setProject(projectData)

      console.log(`[Canvas] 加载项目数据:`, {
        id: projectData.id,
        name: projectData.name,
        status: projectData.status,
        hasGameDesign: !!projectData.gameDesign,
        hasGeneratedCode: !!projectData.generatedCode,
        skipAutoGenerate
      })

      // 根据项目状态采取不同的处理策略
      if (projectData.status === 'creating' && !skipAutoGenerate) {
        console.log(`[Canvas] 项目状态为creating，将在组件挂载后启动生成`)
        // 设置标志，在useEffect中启动生成
        setIsGenerating(false) // 先设置为false，避免重复启动
      } else if (projectData.status === 'completed') {
        console.log(`[Canvas] 项目已完成，设置游戏为已加载状态`)
        setGameLoaded(true)
        setGenerationProgress(100)
        setIsGenerating(false) // 确保生成状态正确

        // 只有在项目完成后才尝试加载生成的游戏组件
        if (projectData.id && !gameComponent) { // 只有在还没有加载组件时才尝试加载
          console.log(`[Canvas] 项目完成，尝试加载组件: ${projectData.id}`)
          const loaded = await loadGameComponent(projectData.id)
          if (loaded) {
            console.log(`[Canvas] 组件加载成功`)
          } else {
            console.warn(`[Canvas] 组件加载失败，将使用默认场景`)
          }
        }
      } else if (projectData.status === 'generating' || projectData.status === 'designing') {
        console.log(`[Canvas] 项目正在生成中 (${projectData.status})，等待完成`)
        setIsGenerating(true)
        // 可以在这里添加轮询逻辑来检查生成状态
      } else if (projectData.status === 'error') {
        console.error(`[Canvas] 项目生成失败`)
        setIsGenerating(false)
        // 可以在这里显示错误信息
      } else {
        console.log(`[Canvas] 项目状态: ${projectData.status}，等待进一步处理`)
      }
      
    } catch (error) {
      console.error('加载项目失败:', error)
    } finally {
      setIsLoading(false)
    }
  }, [projectId, validateProjectId, loadGameComponent, gameComponent])

  // 添加聊天消息
  const addChatMessage = useCallback((type: 'user' | 'agent', content: string, role?: AgentRole) => {
    const message: ChatMessage = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      type,
      role: type === 'agent' ? (role || currentAgent) : undefined,
      content,
      timestamp: new Date(),
      status: type === 'user' ? 'sent' : undefined
    }
    setChatMessages(prev => [...prev, message])
  }, [currentAgent])

  // 启动游戏生成（流式版本）
  const startGameGeneration = useCallback(async () => {
    if (!projectId || !project) return

    setIsGenerating(true)
    setIsStreamingActive(true)
    setGenerationProgress(0)

    // 重置流式输出状态
    setCurrentStreamBuffer("")
    setStreamingMessageId(null)
    setCurrentPhase('design')
    setCurrentAgent('system')

    try {
      // 使用项目专用的流式API，确保ID一致性
      const response = await fetch(`/api/projects/${projectId}/generate-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          description: project.description,
          preferences: {
            gameType: "action",
            difficulty: "medium",
            theme: "现代风格",
            controls: ["触屏", "键盘"]
          }
        })
      })

      if (!response.ok) {
        throw new Error('启动游戏生成失败')
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error('无法获取响应流')
      }

      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              switch (data.type) {
                case 'token':
                  // 累积当前流式响应的所有token
                  setCurrentStreamBuffer(prev => prev + data.data)

                  // 使用currentAgent而不是重新计算，确保角色一致性
                  const agentRole: AgentRole = currentAgent

                  // 创建或更新流式消息
                  setChatMessages(prevMessages => {
                    const newMessages = [...prevMessages]

                    // 检查最后一条消息是否是同一个Agent的流式消息
                    const lastMessage = newMessages[newMessages.length - 1]
                    const canAppendToLastMessage = lastMessage &&
                      lastMessage.isStreaming &&
                      lastMessage.type === 'agent' &&
                      lastMessage.role === agentRole

                    if (canAppendToLastMessage) {
                      // 追加到现有消息 - 流式输出时使用简单格式化
                      const currentContent = lastMessage.content || ''
                      const newContent = currentContent + data.data

                      newMessages[newMessages.length - 1] = {
                        ...lastMessage,
                        content: formatStreamingContentSimple(newContent, currentPhase)
                      }

                      // 确保streamingMessageId指向正确的消息
                      if (!streamingMessageId) {
                        setStreamingMessageId(lastMessage.id)
                      }
                    } else {
                      // 创建新的流式消息 - 流式输出时使用简单格式化
                      const messageId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
                      setStreamingMessageId(messageId)

                      const streamMessage: ChatMessage = {
                        id: messageId,
                        type: 'agent',
                        role: agentRole,
                        content: formatStreamingContentSimple(data.data, currentPhase),
                        timestamp: new Date(),
                        isStreaming: true
                      }
                      newMessages.push(streamMessage)
                    }

                    return newMessages
                  })
                  break

                case 'status':
                  // 显示状态更新并判断阶段
                  const status = data.data as string

                  // 根据状态判断当前阶段和Agent角色
                  if (status.includes('游戏设计') || status.includes('设计')) {
                    setCurrentPhase('design')
                    setCurrentAgent('designer')
                    // 只有在阶段切换时才重置流式消息ID
                    if (currentPhase !== 'design') {
                      setCurrentStreamBuffer("")
                      setStreamingMessageId(null)
                    }
                    addChatMessage('agent', `📋 ${status}`, 'designer')
                  } else if (status.includes('代码生成') || status.includes('生成游戏代码')) {
                    setCurrentPhase('code')
                    setCurrentAgent('coder')
                    // 只有在阶段切换时才重置流式消息ID
                    if (currentPhase !== 'code') {
                      setCurrentStreamBuffer("")
                      setStreamingMessageId(null)
                    }
                    addChatMessage('agent', `📋 ${status}`, 'coder')
                  } else {
                    addChatMessage('agent', `📋 ${status}`, 'system')
                  }
                  break

                case 'progress':
                  if (data.data.current && data.data.total) {
                    const progress = Math.round((data.data.current / data.data.total) * 100)
                    setGenerationProgress(progress)
                    addChatMessage('agent', `⏳ 进度: ${progress}% (${data.data.current}/${data.data.total})`)
                  }
                  break

                case 'complete':
                  // 清理流式状态
                  setStreamingMessageId(null)
                  setCurrentPhase('complete')
                  setIsStreamingActive(false)

                  const result = data.data
                  if (result.success) {
                    setGameLoaded(true)
                    setGenerationProgress(100)

                    // 更新最后一条流式消息为完成状态
                    setChatMessages(prev => {
                      const newMessages = [...prev]
                      if (streamingMessageId) {
                        const messageIndex = newMessages.findIndex(msg => msg.id === streamingMessageId)
                        if (messageIndex !== -1 && newMessages[messageIndex].isStreaming) {
                          const finalContent = currentPhase === 'code'
                            ? `✅ 代码生成完成！\n\n生成的文件: ${Object.keys(result.files || {}).join(', ')}\n\n总计 ${currentStreamBuffer.length} 字符`
                            : `✅ 设计完成！\n\n${formatFinalContent(currentStreamBuffer, 'design')}\n\n总计 ${currentStreamBuffer.length} 字符`

                          newMessages[messageIndex] = {
                            ...newMessages[messageIndex],
                            content: finalContent,
                            isStreaming: false
                          }
                        }
                      }
                      return newMessages
                    })

                    addChatMessage('agent', `🎉 游戏生成成功！游戏ID: ${result.gameId}`)

                    // 立即重新从服务器同步项目状态，确保状态一致性
                    console.log(`[Canvas] 生成完成，重新同步项目状态: ${result.gameId}`)
                    try {
                      await loadProject(true) // 重新从服务器获取最新的项目状态，跳过自动生成
                      console.log(`[Canvas] 项目状态同步完成`)
                    } catch (syncError) {
                      console.error(`[Canvas] 项目状态同步失败:`, syncError)
                      // 如果同步失败，至少更新本地状态
                      setProject(prev => prev ? {
                        ...prev,
                        status: 'completed' as ProjectStatus,
                        generatedCode: result.files
                      } : null)
                    }

                    // 生成完成后尝试加载组件（带重试机制）
                    if (result.gameId) {
                      console.log(`[Canvas] 生成完成，尝试加载组件: ${result.gameId}`)
                      
                      // 重试机制：给 Next.js 编译时间，最多尝试5次，每次间隔递增
                      const tryLoadComponent = async (attempt: number = 1): Promise<void> => {
                        const maxAttempts = 5
                        // 第一次等待更长时间让 Next.js 编译，后续递增
                        const delay = attempt === 1 ? 3000 : attempt * 1500 // 3秒、3秒、4.5秒、6秒、7.5秒
                        
                        setTimeout(async () => {
                          console.log(`[Canvas] 第${attempt}次尝试加载组件: ${result.gameId}`)
                          
                          // 在尝试导入前再次验证文件存在
                          const fileCheckResponse = await fetch(`/api/check-component?gameId=${result.gameId}`)
                          const fileExists = fileCheckResponse.ok
                          
                          if (!fileExists) {
                            console.warn(`[Canvas] 组件文件不存在，等待编译完成`)
                            if (attempt < maxAttempts) {
                              console.log(`[Canvas] 文件不存在，将在${delay}ms后重试`)
                              tryLoadComponent(attempt + 1)
                            }
                            return
                          }
                          
                          const loaded = await loadGameComponent(result.gameId)
                          
                          if (loaded) {
                            console.log(`[Canvas] 第${attempt}次尝试成功，组件加载完成`)
                          } else if (attempt < maxAttempts) {
                            console.log(`[Canvas] 第${attempt}次尝试失败，将在${delay}ms后重试`)
                            tryLoadComponent(attempt + 1)
                          } else {
                            console.warn(`[Canvas] 所有尝试都失败，将使用默认场景`)
                            addChatMessage('agent', '⚠️ 游戏组件加载失败，请刷新页面重试')
                          }
                        }, delay)
                      }
                      
                      tryLoadComponent()
                    }

                    // 解析生成的代码中的节点信息（示例）
                    if (result.files) {
                      parseGameNodes(result.files)
                    }
                  } else {
                    throw new Error(result.errors?.join(', ') || '游戏生成失败')
                  }
                  break

                case 'error':
                  throw new Error(data.data.message || '流式生成过程中发生错误')
              }
            } catch (parseError) {
              console.warn('解析流式数据失败:', parseError, line)
            }
          }
        }
      }

    } catch (error) {
      console.error('游戏生成失败:', error)

      // 清理流式状态
      setStreamingMessageId(null)
      setCurrentStreamBuffer("")
      setIsStreamingActive(false)

      // 更新最后一条流式消息为错误状态
      setChatMessages(prev => {
        const newMessages = [...prev]
        if (streamingMessageId) {
          const messageIndex = newMessages.findIndex(msg => msg.id === streamingMessageId)
          if (messageIndex !== -1 && newMessages[messageIndex].isStreaming) {
            newMessages[messageIndex] = {
              ...newMessages[messageIndex],
              content: `❌ 生成失败：${error instanceof Error ? error.message : '未知错误'}`,
              isStreaming: false
            }
          }
        }
        return newMessages
      })

      addChatMessage('agent', `❌ 游戏生成失败：${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsGenerating(false)
      setIsStreamingActive(false)
    }
  }, [projectId, project])

  // 发送聊天消息
  const sendChatMessage = useCallback(async () => {
    if (!chatInput.trim() || isChatLoading || !projectId) return

    const userMessage = chatInput.trim()
    setChatInput("")
    addChatMessage('user', userMessage)
    setIsChatLoading(true)

    try {
      // 调用迭代优化API
      const response = await fetch(`/api/projects/${projectId}/iterate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userMessage,
          iterationType: 'modify'
        })
      })

      const result = await response.json()

      if (result.success) {
        // 更新项目数据
        setProject(result.project)

        // 添加Agent响应
        addChatMessage('agent', result.agentResponse)

        // 如果游戏已加载，可以触发重新渲染
        if (gameLoaded) {
          // 这里可以添加重新加载游戏的逻辑
          console.log('游戏已更新，可以重新渲染')
        }
      } else {
        addChatMessage('agent', result.agentResponse || result.error || '处理请求时出现问题')
      }

    } catch (error) {
      console.error('发送消息失败:', error)
      addChatMessage('agent', '抱歉，处理您的请求时出现了技术问题，请稍后重试。')
    } finally {
      setIsChatLoading(false)
    }
  }, [chatInput, isChatLoading, projectId, gameLoaded, addChatMessage])

  // 解析游戏代码中的节点信息
  const parseGameNodes = useCallback((files: Record<string, string>) => {
    // TODO: 这里可以解析生成的代码，提取节点信息
    // 目前只是示例数据
    console.log('解析游戏文件:', Object.keys(files))
    const mockNodes: GameNode[] = [
      {
        id: 'player',
        name: '玩家',
        type: 'player',
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
        model: '/models/player/player.glb',
        visible: true
      },
      {
        id: 'camera',
        name: '主摄像机',
        type: 'camera',
        position: { x: 0, y: 5, z: -10 },
        rotation: { x: -15, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
        visible: true
      }
    ]
    setGameNodes(mockNodes)
  }, [])

  // 更新节点属性
  const updateNodeProperty = useCallback((nodeId: string, property: string, value: unknown) => {
    setGameNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, [property]: value }
        : node
    ))
    // 这里可以同步更新到Babylon.js场景
  }, [])

  // 上传资源文件
  const uploadAsset = useCallback(async (file: File) => {
    // 实现文件上传逻辑
    console.log('上传文件:', file.name)
    
    // 模拟上传
    const mockAsset: AssetFile = {
      id: `asset_${Date.now()}`,
      name: file.name,
      type: file.name.endsWith('.glb') ? 'model' : 'texture',
      path: `/assets/${file.name}`,
      size: file.size,
      uploadedAt: new Date()
    }
    
    setAssets(prev => [...prev, mockAsset])
  }, [])

  // 模拟进度更新
  useEffect(() => {
    if (isGenerating && generationProgress < 100) {
      const timer = setTimeout(() => {
        setGenerationProgress(prev => Math.min(prev + Math.random() * 10, 95))
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [isGenerating, generationProgress])

  // 初始化
  useEffect(() => {
    loadProject()
  }, [loadProject])

  // 处理项目状态为creating时的自动生成
  useEffect(() => {
    if (project && project.status === 'creating' && !isGenerating) {
      console.log(`[Canvas] 检测到项目状态为creating，启动生成`)
      startGameGeneration()
    }
  }, [project, isGenerating, startGameGeneration])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false)
      }
      if (event.key === 'Enter' && event.ctrlKey && chatInput.trim()) {
        sendChatMessage()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isFullscreen, chatInput, sendChatMessage])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 mx-auto mb-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
          <p className="text-gray-600">加载项目中...</p>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">项目不存在</h2>
          <p className="text-gray-600 mb-4">请检查项目ID是否正确</p>
          <Button onClick={() => window.location.href = '/create'}>
            返回创建页面
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`h-[calc(100vh-80px)] bg-gray-50 overflow-hidden flex flex-col ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* 顶部工具栏 - 最小化高度 */}
      <div className="bg-white border-b border-gray-200 px-4 py-1 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-sm font-semibold text-gray-900">{project.name}</h1>
            <Badge variant={
              project.status === 'completed' ? 'default' :
              project.status === 'error' ? 'destructive' :
              'secondary'
            }>
              {project.status === 'creating' && '创建中'}
              {project.status === 'designing' && '设计中'}
              {project.status === 'generating' && '生成中'}
              {project.status === 'completed' && '已完成'}
              {project.status === 'error' && '错误'}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            {gameLoaded && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                <Button variant="outline" size="sm">
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </>
            )}
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-1 min-h-0">
        {/* 左侧AI Agent对话区域 - 固定宽度45%，调低高度 */}
        <div className="w-[45%] bg-white border-r border-gray-200 flex flex-col max-h-[calc(100vh-100px)]">
          {/* 对话区域标题 */}
          <div className="border-b border-gray-200 px-4 py-1 flex-shrink-0">
            <div className="flex items-center justify-between">
                              <h2 className="text-sm font-semibold text-gray-900">AI Agent 对话</h2>
              <div className="flex items-center gap-2">
                {isStreamingActive && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>
                      {currentAgent === 'designer' && '设计狮正在工作...'}
                      {currentAgent === 'coder' && '程序媛正在工作...'}
                      {currentAgent === 'system' && 'PM处理中...'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 聊天消息区域 - 使用固定高度和滚动 */}
          <ScrollArea ref={scrollAreaRef} className="flex-1 px-4 min-h-0 max-h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              {chatMessages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex gap-3 max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                    {/* 头像和角色标识 */}
                    <div className="flex flex-col items-center gap-1">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : message.role === 'designer'
                          ? 'bg-purple-500 text-white'
                          : message.role === 'coder'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-500 text-white'
                      }`}>
                        {message.type === 'user' ? (
                          <User className="w-4 h-4" />
                        ) : message.role === 'designer' ? (
                          <Palette className="w-4 h-4" />
                        ) : message.role === 'coder' ? (
                          <Code className="w-4 h-4" />
                        ) : (
                          <Bot className="w-4 h-4" />
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {message.type === 'user'
                          ? '用户'
                          : message.role === 'designer'
                          ? '设计狮'
                          : message.role === 'coder'
                          ? '程序媛'
                          : 'PM'
                        }
                      </span>
                    </div>

                    {/* 消息内容 */}
                    <div className={`rounded-lg px-4 py-3 ${
                      message.type === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <div className="text-sm">
                        {message.role && message.type === 'agent' && (
                          <span className="font-medium text-xs opacity-75 block mb-2">
                            [{message.role === 'designer' ? '设计狮' : message.role === 'coder' ? '程序媛' : 'PM'}]：
                          </span>
                        )}

                        {/* 根据消息状态选择渲染方式 */}
                        {message.isStreaming ? (
                          // 流式消息：直接显示原始内容，不进行markdown渲染
                          <div
                            ref={(el) => {
                              if (el && message.id) {
                                streamingMessageRefs.current.set(message.id, el)
                              }
                            }}
                            className="whitespace-pre-wrap max-h-96 overflow-y-auto font-mono text-sm"
                          >
                            {message.content}
                          </div>
                        ) : message.content.includes('```') ? (
                          // 完成的消息且包含代码块：使用改进的markdown渲染
                          <div className="prose max-w-none text-sm">
                            <ReactMarkdown
                              components={{
                                code: ({ children, className, ...props }) => {
                                  // 内联代码
                                  if (!className) {
                                    return (
                                      <code className="bg-gray-200 px-1 py-0.5 rounded text-xs" {...props}>
                                        {children}
                                      </code>
                                    )
                                  }
                                  // 代码块使用自定义组件
                                  return <CodeBlock className={className} {...props}>{children}</CodeBlock>
                                }
                              }}
                            >
                              {message.content}
                            </ReactMarkdown>
                          </div>
                        ) : (
                          // 完成的消息且为普通文本
                          <div className="whitespace-pre-wrap max-h-96 overflow-y-auto text-sm">
                            {message.content}
                          </div>
                        )}

                        {message.isStreaming && (
                          <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse">|</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {isChatLoading && (
                <div className="flex gap-3 justify-start">
                  <div className="flex gap-3 max-w-[85%]">
                    <div className="w-8 h-8 rounded-full bg-gray-500 flex items-center justify-center">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-gray-100 rounded-lg px-4 py-3">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 滚动定位元素 */}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* 回到底部按钮 */}
          {showScrollToBottom && (
            <div className="relative">
              <Button
                onClick={forceScrollToBottom}
                size="sm"
                variant="outline"
                className="absolute -top-12 right-4 z-10 shadow-lg bg-white hover:bg-gray-50"
              >
                <ChevronDown className="w-4 h-4 mr-1" />
                回到底部
              </Button>
            </div>
          )}

          {/* 输入区域 - 调整为更高的样式 */}
          <div className="border-t border-gray-200 p-2 flex-shrink-0">
                        <div className="space-y-1">
              {/* 主输入框容器 - 包含输入框和发送按钮 */}
              <div className="relative">
                <Textarea
                  placeholder="输入消息与AI Agent对话，描述你想要调整的游戏内容..."
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      sendChatMessage()
                    }
                  }}
                  disabled={isChatLoading}
                  className="min-h-[80px] resize-none pr-12 text-sm"
                  rows={2}
                />
                <Button
                  onClick={sendChatMessage}
                  disabled={!chatInput.trim() || isChatLoading}
                  size="sm"
                  className="absolute right-2 bottom-2"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
              
              {/* 提示信息和其他按钮 */}
              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-500">
                  按 Enter 发送，Shift+Enter 换行
                </div>
                <div className="flex gap-2">
                  {!gameLoaded && !isGenerating && (
                    <Button
                      onClick={startGameGeneration}
                      variant="outline"
                      size="sm"
                    >
                      <Play className="w-4 h-4 mr-1" />
                      开始生成
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧tab区域 - 占用剩余55%宽度，调低高度 */}
        <div className="w-[55%] bg-white flex flex-col max-h-[calc(100vh-100px)]">
                                             <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col max-h-[calc(100vh-120px)]">
            {/* Tab头部 */}
            <div className="border-b border-gray-200 px-4 py-1 flex-shrink-0">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Monitor className="w-4 h-4" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="nodes" className="flex items-center gap-2">
                  <Boxes className="w-4 h-4" />
                  Nodes
                </TabsTrigger>
                <TabsTrigger value="assets" className="flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  Assets
                </TabsTrigger>
                <TabsTrigger value="code" className="flex items-center gap-2">
                  <FileCode className="w-4 h-4" />
                  Code
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab内容区域 */}
            <div className="flex-1 min-h-0 max-h-[calc(100vh-160px)]">
              {/* Preview Tab */}
              <TabsContent value="preview" className="max-h-[calc(100vh-180px)] m-0 data-[state=active]:flex flex-col">
                <div className="bg-black relative max-h-[calc(100vh-180px)] overflow-hidden">
                  {gameLoaded ? (
                    gameComponent ? (
                      // 渲染生成的游戏组件
                      React.createElement(gameComponent)
                    ) : componentLoadError ? (
                      // 显示组件加载错误
                      <div className="w-full h-[calc(100vh-180px)] flex items-center justify-center text-white">
                        <div className="text-center max-w-md">
                          <AlertCircle className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                          <h3 className="text-xl font-semibold mb-2">组件加载失败</h3>
                          <p className="text-gray-300 mb-4">{componentLoadError}</p>
                          <div className="space-y-2">
                            <Button 
                              onClick={() => project?.id && loadGameComponent(project.id)}
                              variant="outline"
                              className="text-white border-white hover:bg-white hover:text-black"
                            >
                              重新加载组件
                            </Button>
                            <p className="text-sm text-gray-400">
                              或者使用默认场景预览
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      // 渲染默认的Three.js Canvas
                      <ThreeCanvas
                        className="w-full h-[calc(100vh-180px)]"
                      />
                    )
                  ) : (
                    <div className="w-full h-[calc(100vh-180px)] flex items-center justify-center text-white">
                      {isGenerating ? (
                        <div className="text-center">
                          <div className="w-16 h-16 mx-auto mb-4 animate-spin rounded-full border-4 border-white border-t-transparent" />
                          <h3 className="text-xl font-semibold mb-2">正在生成您的游戏...</h3>
                          <p className="text-gray-300 mb-4">AI正在根据您的描述创建游戏</p>
                          <div className="w-64 mx-auto">
                            <Progress value={generationProgress} className="bg-gray-700" />
                            <p className="text-sm text-gray-400 mt-2">{generationProgress.toFixed(0)}% 完成</p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center">
                          <Target className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                          <h3 className="text-xl font-semibold mb-2">准备开始生成</h3>
                          <p className="text-gray-300 mb-4">在左侧对话框中描述您的游戏创意</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Nodes Tab */}
              <TabsContent value="nodes" className="max-h-[calc(100vh-180px)] m-0 p-2 data-[state=active]:flex flex-col overflow-hidden">
                <div className="flex-1 space-y-4">
                  {gameNodes.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      <div className="text-center">
                        <Boxes className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                        <h3 className="text-lg font-semibold mb-2">暂无节点数据</h3>
                        <p className="text-sm">游戏生成完成后，节点信息将显示在这里</p>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
                      {/* 节点列表 */}
                      <div className="space-y-2">
                        <h3 className="font-semibold text-gray-900 mb-3">游戏节点</h3>
                        <ScrollArea className="h-[400px]">
                          {gameNodes.map((node) => (
                            <Card 
                              key={node.id} 
                              className={`mb-2 cursor-pointer transition-colors ${
                                selectedNode?.id === node.id ? 'ring-2 ring-blue-500' : ''
                              }`}
                              onClick={() => setSelectedNode(node)}
                            >
                              <CardContent className="p-3">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <div className={`w-3 h-3 rounded-full ${
                                      node.type === 'player' ? 'bg-blue-500' :
                                      node.type === 'camera' ? 'bg-green-500' :
                                      node.type === 'light' ? 'bg-yellow-500' :
                                      node.type === 'enemy' ? 'bg-red-500' :
                                      'bg-gray-500'
                                    }`} />
                                    <span className="font-medium">{node.name}</span>
                                  </div>
                                  <Badge variant="outline" className="text-xs">
                                    {node.type}
                                  </Badge>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </ScrollArea>
                      </div>

                      {/* 节点属性编辑 */}
                      <div>
                        {selectedNode ? (
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">{selectedNode.name} 属性</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                              {/* 位置 */}
                              <div>
                                <label className="text-sm font-medium flex items-center gap-2 mb-2">
                                  <Move3D className="w-4 h-4" />
                                  位置 (Position)
                                </label>
                                <div className="grid grid-cols-3 gap-2">
                                  <div>
                                    <label className="text-xs text-gray-500">X</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.position.x}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'position', {
                                        ...selectedNode.position,
                                        x: parseFloat(e.target.value)
                                      })}
                                      step="0.1"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-gray-500">Y</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.position.y}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'position', {
                                        ...selectedNode.position,
                                        y: parseFloat(e.target.value)
                                      })}
                                      step="0.1"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-gray-500">Z</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.position.z}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'position', {
                                        ...selectedNode.position,
                                        z: parseFloat(e.target.value)
                                      })}
                                      step="0.1"
                                    />
                                  </div>
                                </div>
                              </div>

                              {/* 旋转 */}
                              <div>
                                <label className="text-sm font-medium flex items-center gap-2 mb-2">
                                  <RotateCw className="w-4 h-4" />
                                  旋转 (Rotation)
                                </label>
                                <div className="grid grid-cols-3 gap-2">
                                  <div>
                                    <label className="text-xs text-gray-500">X</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.rotation.x}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'rotation', {
                                        ...selectedNode.rotation,
                                        x: parseFloat(e.target.value)
                                      })}
                                      step="1"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-gray-500">Y</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.rotation.y}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'rotation', {
                                        ...selectedNode.rotation,
                                        y: parseFloat(e.target.value)
                                      })}
                                      step="1"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-gray-500">Z</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.rotation.z}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'rotation', {
                                        ...selectedNode.rotation,
                                        z: parseFloat(e.target.value)
                                      })}
                                      step="1"
                                    />
                                  </div>
                                </div>
                              </div>

                              {/* 缩放 */}
                              <div>
                                <label className="text-sm font-medium flex items-center gap-2 mb-2">
                                  <Scale className="w-4 h-4" />
                                  缩放 (Scale)
                                </label>
                                <div className="grid grid-cols-3 gap-2">
                                  <div>
                                    <label className="text-xs text-gray-500">X</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.scale.x}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'scale', {
                                        ...selectedNode.scale,
                                        x: parseFloat(e.target.value)
                                      })}
                                      step="0.1"
                                      min="0.1"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-gray-500">Y</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.scale.y}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'scale', {
                                        ...selectedNode.scale,
                                        y: parseFloat(e.target.value)
                                      })}
                                      step="0.1"
                                      min="0.1"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-gray-500">Z</label>
                                    <Input
                                      type="number"
                                      value={selectedNode.scale.z}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'scale', {
                                        ...selectedNode.scale,
                                        z: parseFloat(e.target.value)
                                      })}
                                      step="0.1"
                                      min="0.1"
                                    />
                                  </div>
                                </div>
                              </div>

                              {/* 资源配置 */}
                              {selectedNode.type !== 'camera' && selectedNode.type !== 'light' && (
                                <div className="space-y-3">
                                  <Separator />
                                  <div>
                                    <label className="text-sm font-medium mb-2 block">模型文件</label>
                                    <Input
                                      value={selectedNode.model || ''}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'model', e.target.value)}
                                      placeholder="/models/example.glb"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium mb-2 block">材质</label>
                                    <Input
                                      value={selectedNode.material || ''}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'material', e.target.value)}
                                      placeholder="材质名称"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium mb-2 block">动画</label>
                                    <Input
                                      value={selectedNode.animation || ''}
                                      onChange={(e) => updateNodeProperty(selectedNode.id, 'animation', e.target.value)}
                                      placeholder="动画名称"
                                    />
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        ) : (
                          <div className="flex items-center justify-center h-full text-gray-500">
                            <div className="text-center">
                              <Settings className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                              <p>选择一个节点来编辑其属性</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Assets Tab */}
              <TabsContent value="assets" className="max-h-[calc(100vh-180px)] m-0 p-2 data-[state=active]:flex flex-col overflow-hidden">
                <div className="flex-1 space-y-4">
                  {/* 上传区域 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Upload className="w-5 h-5" />
                        资源上传
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                        <p className="text-gray-600 mb-2">点击上传或拖拽文件到这里</p>
                        <p className="text-sm text-gray-500">
                          支持 .glb, .gltf, .png, .jpg, .mp4 等格式
                        </p>
                        <input
                          type="file"
                          multiple
                          accept=".glb,.gltf,.png,.jpg,.jpeg,.mp4,.mp3"
                          onChange={(e) => {
                            if (e.target.files) {
                              Array.from(e.target.files).forEach(uploadAsset)
                            }
                          }}
                          className="hidden"
                          id="asset-upload"
                        />
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => document.getElementById('asset-upload')?.click()}
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          选择文件
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 资源列表 */}
                  <Card className="flex-1">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FolderOpen className="w-5 h-5" />
                        资源管理
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {assets.length === 0 ? (
                        <div className="flex items-center justify-center h-32 text-gray-500">
                          <div className="text-center">
                            <Package className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                            <p>暂无上传的资源文件</p>
                          </div>
                        </div>
                      ) : (
                        <ScrollArea className="h-[300px]">
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            {assets.map((asset) => (
                              <Card key={asset.id} className="p-3">
                                <div className="flex items-center gap-3">
                                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                    asset.type === 'model' ? 'bg-blue-100 text-blue-600' :
                                    asset.type === 'texture' ? 'bg-green-100 text-green-600' :
                                    asset.type === 'animation' ? 'bg-purple-100 text-purple-600' :
                                    'bg-gray-100 text-gray-600'
                                  }`}>
                                    {asset.type === 'model' ? (
                                      <Boxes className="w-5 h-5" />
                                    ) : (
                                      <Package className="w-5 h-5" />
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-medium text-sm truncate">{asset.name}</h4>
                                    <p className="text-xs text-gray-500">
                                      {(asset.size / 1024).toFixed(1)} KB
                                    </p>
                                  </div>
                                </div>
                              </Card>
                            ))}
                          </div>
                        </ScrollArea>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Code Tab */}
              <TabsContent value="code" className="max-h-[calc(100vh-180px)] m-0 p-2 data-[state=active]:flex flex-col overflow-hidden">
                <div className="flex-1">
                  {project?.generatedCode ? (
                    <Card className="h-full">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <FileCode className="w-5 h-5" />
                          生成的代码
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="h-[calc(100%-80px)]">
                        <ScrollArea className="h-full">
                          <div className="space-y-4">
                            {Object.entries(project.generatedCode).map(([filename, code]) => (
                              <div key={filename}>
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-sm">{filename}</h4>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => navigator.clipboard.writeText(code)}
                                  >
                                    复制代码
                                  </Button>
                                </div>
                                <SyntaxHighlighter
                                  language="typescript"
                                  style={vscDarkPlus}
                                  className="rounded-lg text-sm"
                                >
                                  {code}
                                </SyntaxHighlighter>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      <div className="text-center">
                        <FileCode className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                        <h3 className="text-lg font-semibold mb-2">暂无代码数据</h3>
                        <p className="text-sm">游戏生成完成后，代码将显示在这里</p>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
