/**
 * 代码优化管理器
 * 专门处理Three.js和React代码的优化
 */

import { QualityReport, QualityIssue } from '../quality/CodeQualityChecker';

export interface OptimizationRule {
  id: string;
  name: string;
  description: string;
  category: 'performance' | 'memory' | 'readability' | 'security';
  severity: 'low' | 'medium' | 'high';
  pattern: RegExp;
  replacement: string | ((match: string) => string);
  enabled: boolean;
}

export interface OptimizationResult {
  originalCode: string;
  optimizedCode: string;
  appliedRules: string[];
  improvements: {
    performanceGain: number;
    memoryReduction: number;
    readabilityScore: number;
  };
  warnings: string[];
}

/**
 * 代码优化管理器
 */
export class OptimizationManager {
  private rules: OptimizationRule[];

  constructor() {
    this.rules = this.initializeOptimizationRules();
  }

  /**
   * 初始化优化规则
   */
  private initializeOptimizationRules(): OptimizationRule[] {
    return [
      // Three.js性能优化规则
      {
        id: 'threejs-dispose-resources',
        name: 'Three.js资源释放',
        description: '自动添加Three.js对象的dispose调用',
        category: 'memory',
        severity: 'high',
        pattern: /new THREE\.(.*?)(Geometry|Material|Texture)\(/g,
        replacement: (match) => {
          // 这里需要更复杂的逻辑来添加dispose调用
          return match;
        },
        enabled: true
      },
      {
        id: 'threejs-animation-loop',
        name: '优化动画循环',
        description: '确保使用requestAnimationFrame进行动画',
        category: 'performance',
        severity: 'high',
        pattern: /setInterval\s*\(\s*.*?renderer\.render/g,
        replacement: 'requestAnimationFrame(animate); // 使用requestAnimationFrame替代setInterval',
        enabled: true
      },
      {
        id: 'threejs-camera-update',
        name: '相机矩阵更新',
        description: '在修改相机属性后自动添加updateProjectionMatrix调用',
        category: 'performance',
        severity: 'medium',
        pattern: /(camera\.aspect\s*=.*?);(?!\s*camera\.updateProjectionMatrix)/g,
        replacement: '$1;\n  camera.updateProjectionMatrix();',
        enabled: true
      },
      {
        id: 'threejs-controls-update',
        name: '控制器更新',
        description: '在动画循环中添加控制器更新',
        category: 'performance',
        severity: 'medium',
        pattern: /(function\s+animate\s*\(\s*\)\s*{[^}]*renderer\.render[^}]*)(})/g,
        replacement: '$1  controls.update();\n$2',
        enabled: true
      },

      // React优化规则
      {
        id: 'react-usecallback',
        name: '使用useCallback优化',
        description: '为函数添加useCallback包装',
        category: 'performance',
        severity: 'medium',
        pattern: /const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*{/g,
        replacement: 'const $1 = useCallback(($2) => {',
        enabled: false // 默认关闭，因为需要手动判断依赖
      },
      {
        id: 'react-usememo',
        name: '使用useMemo优化',
        description: '为计算密集型操作添加useMemo',
        category: 'performance',
        severity: 'medium',
        pattern: /const\s+(\w+)\s*=\s*(new\s+THREE\.[^;]+);/g,
        replacement: 'const $1 = useMemo(() => $2, []);',
        enabled: false // 默认关闭，需要手动判断依赖
      },

      // 通用优化规则
      {
        id: 'remove-console',
        name: '移除console语句',
        description: '移除生产环境中的console.log语句',
        category: 'performance',
        severity: 'low',
        pattern: /console\.(log|warn|info)\([^)]*\);?\s*\n?/g,
        replacement: '',
        enabled: true
      },
      {
        id: 'remove-debugger',
        name: '移除debugger语句',
        description: '移除debugger语句',
        category: 'performance',
        severity: 'medium',
        pattern: /debugger;?\s*\n?/g,
        replacement: '',
        enabled: true
      },
      {
        id: 'optimize-imports',
        name: '优化导入语句',
        description: '合并和优化import语句',
        category: 'readability',
        severity: 'low',
        pattern: /import\s*{\s*([^}]+)\s*}\s*from\s*['"]three['"];?\s*\n\s*import\s*{\s*([^}]+)\s*}\s*from\s*['"]three['"];?/g,
        replacement: 'import { $1, $2 } from \'three\';',
        enabled: true
      },

      // TypeScript优化规则
      {
        id: 'typescript-explicit-types',
        name: '添加显式类型',
        description: '为变量添加显式类型注解',
        category: 'readability',
        severity: 'low',
        pattern: /const\s+(\w+)\s*=\s*new\s+THREE\.(\w+)\(/g,
        replacement: 'const $1: THREE.$2 = new THREE.$2(',
        enabled: false // 默认关闭，可能会产生冗余类型
      },
      {
        id: 'typescript-remove-any',
        name: '移除any类型',
        description: '尝试替换any类型为更具体的类型',
        category: 'readability',
        severity: 'medium',
        pattern: /:\s*any\b/g,
        replacement: ': unknown', // 更安全的替代
        enabled: false // 默认关闭，需要手动处理
      }
    ];
  }

  /**
   * 优化代码
   */
  optimizeCode(code: string, qualityReport?: QualityReport): OptimizationResult {
    let optimizedCode = code;
    const appliedRules: string[] = [];
    const warnings: string[] = [];

    // 应用启用的优化规则
    for (const rule of this.rules.filter(r => r.enabled)) {
      try {
        const beforeOptimization = optimizedCode;
        
        if (typeof rule.replacement === 'string') {
          optimizedCode = optimizedCode.replace(rule.pattern, rule.replacement);
        } else {
          optimizedCode = optimizedCode.replace(rule.pattern, rule.replacement);
        }

        // 如果代码发生了变化，记录应用的规则
        if (beforeOptimization !== optimizedCode) {
          appliedRules.push(rule.id);
          console.log(`[OptimizationManager] 应用规则: ${rule.name}`);
        }
      } catch (error) {
        warnings.push(`规则 ${rule.id} 应用失败: ${error}`);
        console.warn(`[OptimizationManager] 规则应用失败:`, error);
      }
    }

    // 基于质量报告进行特定优化
    if (qualityReport) {
      const specificOptimizations = this.applyQualityBasedOptimizations(optimizedCode, qualityReport);
      optimizedCode = specificOptimizations.code;
      appliedRules.push(...specificOptimizations.appliedRules);
      warnings.push(...specificOptimizations.warnings);
    }

    // 计算改进指标
    const improvements = this.calculateImprovements(code, optimizedCode);

    return {
      originalCode: code,
      optimizedCode,
      appliedRules,
      improvements,
      warnings
    };
  }

  /**
   * 基于质量报告应用特定优化
   */
  private applyQualityBasedOptimizations(
    code: string, 
    qualityReport: QualityReport
  ): { code: string; appliedRules: string[]; warnings: string[] } {
    let optimizedCode = code;
    const appliedRules: string[] = [];
    const warnings: string[] = [];

    for (const issue of qualityReport.issues) {
      try {
        const optimization = this.getOptimizationForIssue(issue);
        if (optimization) {
          const beforeOptimization = optimizedCode;
          optimizedCode = optimization.apply(optimizedCode);
          
          if (beforeOptimization !== optimizedCode) {
            appliedRules.push(`quality-fix-${issue.category}`);
          }
        }
      } catch (error) {
        warnings.push(`质量问题修复失败: ${issue.message}`);
      }
    }

    return { code: optimizedCode, appliedRules, warnings };
  }

  /**
   * 为特定问题获取优化方案
   */
  private getOptimizationForIssue(issue: QualityIssue): { apply: (code: string) => string } | null {
    switch (issue.category) {
      case 'threejs':
        if (issue.message.includes('dispose')) {
          return {
            apply: (code: string) => {
              // 在useEffect清理函数中添加dispose调用
              if (!code.includes('return () =>')) {
                return code.replace(
                  /useEffect\(\(\) => {([^}]+)}, \[\]\);/,
                  'useEffect(() => {$1\n    return () => {\n      // 清理Three.js资源\n      // TODO: 添加具体的dispose调用\n    };\n  }, []);'
                );
              }
              return code;
            }
          };
        }
        break;

      case 'react':
        if (issue.message.includes('dependencies')) {
          return {
            apply: (code: string) => {
              // 这里可以添加依赖分析和修复逻辑
              return code;
            }
          };
        }
        break;

      case 'performance':
        if (issue.message.includes('console')) {
          return {
            apply: (code: string) => {
              return code.replace(/console\.(log|warn|info)\([^)]*\);?\s*\n?/g, '');
            }
          };
        }
        break;

      default:
        return null;
    }

    return null;
  }

  /**
   * 计算优化改进指标
   */
  private calculateImprovements(originalCode: string, optimizedCode: string): {
    performanceGain: number;
    memoryReduction: number;
    readabilityScore: number;
  } {
    // 简单的指标计算
    const originalLines = originalCode.split('\n').length;
    const optimizedLines = optimizedCode.split('\n').length;
    
    // 性能提升（基于移除的console语句和优化的模式）
    const consoleStatementsRemoved = (originalCode.match(/console\./g) || []).length - 
                                   (optimizedCode.match(/console\./g) || []).length;
    const performanceGain = Math.min(consoleStatementsRemoved * 5, 50); // 最多50%提升

    // 内存减少（基于添加的dispose调用）
    const disposeCallsAdded = (optimizedCode.match(/\.dispose\(\)/g) || []).length - 
                             (originalCode.match(/\.dispose\(\)/g) || []).length;
    const memoryReduction = Math.min(disposeCallsAdded * 10, 30); // 最多30%减少

    // 可读性分数（基于代码结构改进）
    const readabilityScore = Math.max(0, Math.min(100, 
      80 + (originalLines - optimizedLines) * 2 // 代码行数减少提升可读性
    ));

    return {
      performanceGain,
      memoryReduction,
      readabilityScore
    };
  }

  /**
   * 获取所有优化规则
   */
  getRules(): OptimizationRule[] {
    return [...this.rules];
  }

  /**
   * 启用/禁用优化规则
   */
  toggleRule(ruleId: string, enabled: boolean): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = enabled;
    }
  }

  /**
   * 添加自定义优化规则
   */
  addCustomRule(rule: OptimizationRule): void {
    this.rules.push(rule);
  }

  /**
   * 移除优化规则
   */
  removeRule(ruleId: string): void {
    this.rules = this.rules.filter(r => r.id !== ruleId);
  }

  /**
   * 获取优化统计
   */
  getOptimizationStats(): {
    totalRules: number;
    enabledRules: number;
    rulesByCategory: Record<string, number>;
    rulesBySeverity: Record<string, number>;
  } {
    const enabledRules = this.rules.filter(r => r.enabled).length;
    
    const rulesByCategory = this.rules.reduce((acc, rule) => {
      acc[rule.category] = (acc[rule.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const rulesBySeverity = this.rules.reduce((acc, rule) => {
      acc[rule.severity] = (acc[rule.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRules: this.rules.length,
      enabledRules,
      rulesByCategory,
      rulesBySeverity
    };
  }
}
