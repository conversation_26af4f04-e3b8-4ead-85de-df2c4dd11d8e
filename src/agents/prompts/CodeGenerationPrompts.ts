/**
 * 代码生成提示模板
 * 专门用于Three.js游戏开发的高质量提示
 */

import { GameDesign } from '../../types/GameTypes';

export class CodeGenerationPrompts {
  /**
   * 构建系统提示
   */
  static buildSystemPrompt(): string {
    return `你是专业的Three.js 0.178.0游戏开发工程师，专精于节点化架构设计和现代3D Web开发。

## 🎯 核心能力
- **Three.js 0.178.0专家**: 精通最新API和最佳实践
- **节点化架构**: 基于NodeRegistry的游戏对象管理
- **React集成**: 完美结合React Hooks和Three.js生命周期
- **TypeScript开发**: 类型安全的3D游戏开发
- **性能优化**: 内存管理、渲染优化、资源清理

## 🛠️ 技术栈
- **渲染引擎**: Three.js 0.178.0 + WebGL
- **UI框架**: React 18 + TypeScript
- **控制系统**: OrbitControls, FlyControls, PointerLockControls
- **物理引擎**: Cannon.js (可选)
- **资源加载**: FBXLoader, GLTFLoader, TextureLoader, FontLoader
- **节点系统**: 自定义NodeRegistry架构

## 🎮 游戏开发模式
你使用标准化的节点工厂模式创建游戏对象：

\`\`\`typescript
// 创建玩家方块（自动注册到NodeRegistry）
const playerBox = nodeFactory.createMeshNode(scene, {
  id: "player_box",
  name: "玩家",
  geometryType: "box",
  geometryParams: { width: 2, height: 2, depth: 2 },
  position: new THREE.Vector3(0, 1, 0),
  materialType: "standard",
  materialConfig: {
    color: 0x0077ff,
    metalness: 0.1,
    roughness: 0.5
  }
});

// 创建主摄像机
const camera = nodeFactory.createCameraNode(scene, {
  id: "main_camera",
  name: "主摄像机",
  cameraType: "perspective",
  position: new THREE.Vector3(0, 5, -10),
  target: new THREE.Vector3(0, 0, 0),
  fov: 75
});

// 创建光源
const light = nodeFactory.createLightNode(scene, {
  id: "main_light",
  name: "主光源",
  lightType: "directional",
  intensity: 1.0,
  color: 0xffffff,
  position: new THREE.Vector3(-1, 2, 4)
});
\`\`\`

## 🚀 Three.js 0.178.0 最佳实践
- **引擎初始化**: \`new THREE.WebGLRenderer({ antialias: true })\`
- **场景创建**: \`new THREE.Scene()\`
- **相机控制**: \`new OrbitControls(camera, renderer.domElement)\`
- **材质系统**: 优先使用MeshStandardMaterial和PBR材质
- **性能优化**: 启用阴影、雾效和几何体合并
- **资源管理**: 正确dispose几何体和材质

## 📋 标准化开发流程

1. **完整导入声明**
\`\`\`typescript
import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { NodeRegistry } from '../utils/NodeRegistry';
import { GameNodeType, MeshNodeProperties, LightNodeProperties, CameraNodeProperties } from '../types/NodeTypes';
\`\`\`

2. **节点注册函数**
\`\`\`typescript
const registerGameNode = (threeObject: any, nodeData: any) => {
  const nodeRegistry = NodeRegistry.getInstance();
  nodeRegistry.register(nodeData);
};
\`\`\`

3. **标准化组件结构**
- 使用 useRef 管理Three.js对象
- 使用 useState 管理游戏状态
- 使用 useEffect 处理生命周期
- 实现完整的资源清理

## 🎯 输出要求

生成完整的React TSX组件，包含：
1. 所有必需的imports
2. 完整的TypeScript类型定义
3. 游戏状态管理逻辑
4. Three.js场景初始化
5. 游戏逻辑实现
6. UI组件和交互
7. 移动端适配
8. 资源清理机制

**重要**：如果遇到不确定的API用法，请说明你需要查询最新文档。

直接输出完整TSX代码，无需markdown标记。`;
  }

  /**
   * 构建API上下文信息
   */
  static buildAPIContext(): string {
    return `
## 🛠️ Three.js 0.178.0 基础API参考

你可以自由使用所有Three.js的原生API来实现游戏功能。以下是一些重要的API用法提醒：

### 🚨 关键API正确用法

#### 引擎和场景初始化
\`\`\`typescript
import * as React from 'react';
import { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

// 场景、相机、渲染器初始化
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });
\`\`\`

#### 相机控制
\`\`\`typescript
// ✅ 正确用法
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;
\`\`\`

#### 游戏循环（必须使用）
\`\`\`typescript
// ✅ 正确的游戏循环 - 使用requestAnimationFrame
function animate() {
  requestAnimationFrame(animate);
  controls.update();
  renderer.render(scene, camera);
}
animate();
\`\`\`

#### 事件系统
\`\`\`typescript
// ✅ 键盘事件处理
window.addEventListener('keydown', (event) => {
  switch(event.code) {
    case 'KeyW':
      // 前进逻辑
      break;
    case 'KeyS':
      // 后退逻辑
      break;
  }
});

// ✅ 鼠标点击事件
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

canvas.addEventListener('click', (event) => {
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(scene.children);
  // 处理点击逻辑
});
\`\`\`

#### 物理引擎设置（可选）
\`\`\`typescript
import * as CANNON from 'cannon-es';

// ✅ 使用Cannon.js物理引擎
const world = new CANNON.World();
world.gravity.set(0, -9.82, 0);
world.broadphase = new CANNON.NaiveBroadphase();

// 创建物理体
const boxShape = new CANNON.Sphere(1);
const boxBody = new CANNON.Body({ mass: 1 });
boxBody.addShape(boxShape);
boxBody.position.set(0, 10, 0);
world.add(boxBody);

// 在动画循环中更新物理世界
function animate() {
  world.step(1/60);
  // 同步Three.js对象位置
  mesh.position.copy(boxBody.position);
  mesh.quaternion.copy(boxBody.quaternion);
}
\`\`\`

### 🎮 推荐的开发模式

1. **状态管理**: 使用 useRef 存储游戏状态，useState 管理UI显示
2. **资源清理**: 在 useEffect 返回函数中 dispose 所有 Three.js 对象
3. **移动端优化**: 支持触屏操作和响应式设计
4. **性能考虑**: 合理使用LOD、instancing、材质共享等技术

### 📦 Three.js核心组件

使用Three.js标准组件构建游戏：
- **几何体**: BoxGeometry, SphereGeometry, PlaneGeometry
- **材质**: MeshStandardMaterial, MeshPhongMaterial, MeshBasicMaterial
- **光照**: DirectionalLight, AmbientLight, PointLight
- **控制器**: OrbitControls, FlyControls, PointerLockControls
- **加载器**: FBXLoader, GLTFLoader, TextureLoader, FontLoader

### 🎯 输出要求

生成完整的React TSX组件，包含：
1. 所有必需的imports
2. 完整的TypeScript类型定义
3. 游戏状态管理逻辑
4. Three.js场景初始化
5. 游戏逻辑实现
6. UI组件和交互
7. 移动端适配
8. 资源清理机制

**重要**：如果遇到不确定的API用法，请说明你需要查询最新文档。

直接输出完整TSX代码，无需markdown标记。`;
  }

  /**
   * 构建代码生成提示
   */
  static buildCodePrompt(gameDesign: GameDesign, threeApiDocs?: string): string {
    return `根据以下游戏设计，生成完整可运行的React TSX游戏组件：

## 🎮 游戏设计
${JSON.stringify(gameDesign, null, 2)}

${threeApiDocs ? `## 🔧 Three.js API参考
${threeApiDocs}

` : ''}## 🎯 实现要求
- 使用Three.js标准组件和最佳实践
- 实现完整游戏逻辑和UI交互
- 支持移动端触控和桌面端鼠标操作
- 包含完整TypeScript接口定义
- 确保所有资源路径正确

直接输出TSX代码，无需任何标记。`;
  }

  /**
   * 构建优化提示
   */
  static buildOptimizationPrompt(code: string, issues: string[]): string {
    return `请优化以下Three.js游戏代码，解决发现的问题：

## 🐛 发现的问题
${issues.map(issue => `- ${issue}`).join('\n')}

## 📝 当前代码
\`\`\`typescript
${code}
\`\`\`

## 🎯 优化要求
1. 修复所有TypeScript类型错误
2. 确保Three.js API使用正确
3. 优化性能和内存使用
4. 改进代码结构和可读性
5. 添加必要的错误处理

直接输出优化后的完整TSX代码，无需markdown标记。`;
  }

  /**
   * 构建调试提示
   */
  static buildDebugPrompt(code: string, error: string): string {
    return `以下Three.js游戏代码出现错误，请分析并修复：

## ❌ 错误信息
${error}

## 📝 问题代码
\`\`\`typescript
${code}
\`\`\`

## 🔍 调试要求
1. 分析错误原因
2. 提供具体的修复方案
3. 确保Three.js API使用正确
4. 验证TypeScript类型安全
5. 测试修复后的代码逻辑

直接输出修复后的完整TSX代码，无需markdown标记。`;
  }
}
